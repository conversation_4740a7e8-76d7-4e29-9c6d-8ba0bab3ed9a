import type { NextConfigComplete } from '../../server/config-shared';
type EventCliSessionStarted = {
    nextVersion: string;
    nodeVersion: string;
    cliCommand: string;
    isSrcDir: boolean | null;
    hasNowJson: boolean;
    isCustomServer: boolean | null;
    hasNextConfig: boolean;
    buildTarget: string;
    hasWebpackConfig: boolean;
    hasBabelConfig: boolean;
    basePathEnabled: boolean;
    i18nEnabled: boolean;
    imageEnabled: boolean;
    imageFutureEnabled: boolean;
    locales: string | null;
    localeDomainsCount: number | null;
    localeDetectionEnabled: boolean | null;
    imageDomainsCount: number | null;
    imageRemotePatternsCount: number | null;
    imageLocalPatternsCount: number | null;
    imageQualities: string | null;
    imageSizes: string | null;
    imageLoader: string | null;
    imageFormats: string | null;
    nextConfigOutput: string | null;
    trailingSlashEnabled: boolean;
    reactStrictMode: boolean;
    webpackVersion: number | null;
    turboFlag: boolean;
    isRspack: boolean;
    appDir: boolean | null;
    pagesDir: boolean | null;
    staticStaleTime: number | null;
    dynamicStaleTime: number | null;
    reactCompiler: boolean;
    reactCompilerCompilationMode: string | null;
    reactCompilerPanicThreshold: string | null;
};
export declare function eventCliSession(dir: string, nextConfig: NextConfigComplete, event: Omit<EventCliSessionStarted, 'nextVersion' | 'nodeVersion' | 'hasNextConfig' | 'buildTarget' | 'hasWebpackConfig' | 'hasBabelConfig' | 'basePathEnabled' | 'i18nEnabled' | 'imageEnabled' | 'imageFutureEnabled' | 'locales' | 'localeDomainsCount' | 'localeDetectionEnabled' | 'imageDomainsCount' | 'imageRemotePatternsCount' | 'imageLocalPatternsCount' | 'imageQualities' | 'imageSizes' | 'imageLoader' | 'imageFormats' | 'nextConfigOutput' | 'trailingSlashEnabled' | 'reactStrictMode' | 'staticStaleTime' | 'dynamicStaleTime' | 'reactCompiler' | 'reactCompilerCompilationMode' | 'reactCompilerPanicThreshold' | 'isRspack'>): {
    eventName: string;
    payload: EventCliSessionStarted;
}[];
export {};
