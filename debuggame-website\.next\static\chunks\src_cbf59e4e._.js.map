{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DebugGame/debuggame-website/src/types/game.ts"], "sourcesContent": ["// 游戏基础信息类型定义\n\nexport interface Game {\n  id: string;\n  title: string;\n  description: string;\n  cover: string;\n  screenshots: string[];\n  \n  // 基础信息\n  developer: string;\n  publisher: string;\n  releaseDate: string;\n  platforms: Platform[];\n  genres: Genre[];\n  tags: string[];\n  \n  // 评分信息\n  ratings: GameRating[];\n  userRating?: number;\n  \n  // 系统要求\n  systemRequirements?: SystemRequirements;\n  \n  // 获奖信息\n  awards: Award[];\n  \n  // 游戏特色\n  features: string[];\n  \n  // 元数据\n  createdAt: string;\n  updatedAt: string;\n  status: GameStatus;\n}\n\nexport interface Platform {\n  id: string;\n  name: string;\n  icon: string;\n}\n\nexport interface Genre {\n  id: string;\n  name: string;\n  color: string;\n}\n\nexport interface GameRating {\n  source: string; // IGN, GameSpot, Metacritic, etc.\n  score: number;\n  maxScore: number;\n  reviewUrl?: string;\n  reviewDate?: string;\n}\n\nexport interface SystemRequirements {\n  minimum: {\n    os: string;\n    processor: string;\n    memory: string;\n    graphics: string;\n    storage: string;\n  };\n  recommended?: {\n    os: string;\n    processor: string;\n    memory: string;\n    graphics: string;\n    storage: string;\n  };\n}\n\nexport interface Award {\n  id: string;\n  name: string;\n  organization: string;\n  year: number;\n  category: string;\n  icon?: string;\n}\n\nexport enum GameStatus {\n  DRAFT = 'draft',\n  PUBLISHED = 'published',\n  ARCHIVED = 'archived'\n}\n\n// 游戏筛选和搜索\nexport interface GameFilter {\n  search?: string;\n  genres?: string[];\n  platforms?: string[];\n  yearRange?: [number, number];\n  ratingRange?: [number, number];\n  tags?: string[];\n  sortBy?: GameSortBy;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport enum GameSortBy {\n  TITLE = 'title',\n  RELEASE_DATE = 'releaseDate',\n  RATING = 'rating',\n  CREATED_AT = 'createdAt'\n}\n\n// 游戏统计信息\nexport interface GameStats {\n  totalGames: number;\n  genreDistribution: { [key: string]: number };\n  platformDistribution: { [key: string]: number };\n  yearDistribution: { [key: string]: number };\n  averageRating: number;\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;AAkFN,IAAA,AAAK,oCAAA;;;;WAAA;;AAkBL,IAAA,AAAK,oCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DebugGame/debuggame-website/src/lib/gameService.ts"], "sourcesContent": ["import { Game, GameFilter, GameStats, Platform, Genre, GameStatus } from '@/types/game';\n\n// 模拟数据存储 - 后续可以替换为真实的数据库\nlet gamesDatabase: Game[] = [\n  {\n    id: '1',\n    title: 'The Legend of Zelda: Breath of the Wild',\n    description: '任天堂开发的开放世界动作冒险游戏，重新定义了塞尔达传说系列。',\n    cover: '/game-covers/zelda-botw.jpg',\n    screenshots: [],\n    developer: 'Nintendo EPD',\n    publisher: 'Nintendo',\n    releaseDate: '2017-03-03',\n    platforms: [{ id: 'switch', name: 'Nintendo Switch', icon: '/icons/switch.svg' }],\n    genres: [\n      { id: 'action-adventure', name: '动作冒险', color: '#10b981' },\n      { id: 'open-world', name: '开放世界', color: '#3b82f6' }\n    ],\n    tags: ['开放世界', '探索', '解谜', '战斗'],\n    ratings: [\n      { source: 'IGN', score: 10, maxScore: 10, reviewUrl: 'https://ign.com' },\n      { source: 'GameSpot', score: 9, maxScore: 10 },\n      { source: 'Metacritic', score: 97, maxScore: 100 }\n    ],\n    awards: [\n      {\n        id: '1',\n        name: 'Game of the Year',\n        organization: 'The Game Awards',\n        year: 2017,\n        category: '年度游戏'\n      }\n    ],\n    features: ['开放世界探索', '物理引擎', '天气系统', '烹饪系统'],\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-01T00:00:00Z',\n    status: GameStatus.PUBLISHED\n  },\n  {\n    id: '2',\n    title: 'God of War',\n    description: 'Santa Monica Studio开发的动作冒险游戏，讲述奎托斯与儿子阿特柔斯的北欧神话之旅。',\n    cover: '/game-covers/god-of-war.jpg',\n    screenshots: [],\n    developer: 'Santa Monica Studio',\n    publisher: 'Sony Interactive Entertainment',\n    releaseDate: '2018-04-20',\n    platforms: [{ id: 'ps4', name: 'PlayStation 4', icon: '/icons/ps4.svg' }],\n    genres: [\n      { id: 'action-adventure', name: '动作冒险', color: '#10b981' },\n      { id: 'mythology', name: '神话', color: '#f59e0b' }\n    ],\n    tags: ['单人', '剧情', '战斗', '北欧神话'],\n    ratings: [\n      { source: 'IGN', score: 10, maxScore: 10 },\n      { source: 'GameSpot', score: 9, maxScore: 10 },\n      { source: 'Metacritic', score: 94, maxScore: 100 }\n    ],\n    awards: [],\n    features: ['一镜到底', '父子关系', '战斗系统', '解谜元素'],\n    createdAt: '2024-01-02T00:00:00Z',\n    updatedAt: '2024-01-02T00:00:00Z',\n    status: GameStatus.PUBLISHED\n  }\n];\n\n// 游戏管理服务类\nexport class GameService {\n  // 获取所有游戏\n  static async getAllGames(): Promise<Game[]> {\n    return new Promise((resolve) => {\n      setTimeout(() => resolve([...gamesDatabase]), 100);\n    });\n  }\n\n  // 根据ID获取游戏\n  static async getGameById(id: string): Promise<Game | null> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const game = gamesDatabase.find(g => g.id === id);\n        resolve(game || null);\n      }, 100);\n    });\n  }\n\n  // 搜索和筛选游戏\n  static async searchGames(filter: GameFilter): Promise<Game[]> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        let filteredGames = [...gamesDatabase];\n\n        // 搜索标题\n        if (filter.search) {\n          const searchTerm = filter.search.toLowerCase();\n          filteredGames = filteredGames.filter(game =>\n            game.title.toLowerCase().includes(searchTerm) ||\n            game.description.toLowerCase().includes(searchTerm) ||\n            game.developer.toLowerCase().includes(searchTerm)\n          );\n        }\n\n        // 按类型筛选\n        if (filter.genres && filter.genres.length > 0) {\n          filteredGames = filteredGames.filter(game =>\n            game.genres.some(genre => filter.genres!.includes(genre.id))\n          );\n        }\n\n        // 按平台筛选\n        if (filter.platforms && filter.platforms.length > 0) {\n          filteredGames = filteredGames.filter(game =>\n            game.platforms.some(platform => filter.platforms!.includes(platform.id))\n          );\n        }\n\n        // 按年份筛选\n        if (filter.yearRange) {\n          filteredGames = filteredGames.filter(game => {\n            const year = new Date(game.releaseDate).getFullYear();\n            return year >= filter.yearRange![0] && year <= filter.yearRange![1];\n          });\n        }\n\n        // 排序\n        if (filter.sortBy) {\n          filteredGames.sort((a, b) => {\n            let comparison = 0;\n            switch (filter.sortBy) {\n              case 'title':\n                comparison = a.title.localeCompare(b.title);\n                break;\n              case 'releaseDate':\n                comparison = new Date(a.releaseDate).getTime() - new Date(b.releaseDate).getTime();\n                break;\n              case 'rating':\n                const aRating = a.ratings.length > 0 ? a.ratings[0].score : 0;\n                const bRating = b.ratings.length > 0 ? b.ratings[0].score : 0;\n                comparison = aRating - bRating;\n                break;\n              default:\n                comparison = 0;\n            }\n            return filter.sortOrder === 'desc' ? -comparison : comparison;\n          });\n        }\n\n        resolve(filteredGames);\n      }, 200);\n    });\n  }\n\n  // 创建游戏\n  static async createGame(gameData: Omit<Game, 'id' | 'createdAt' | 'updatedAt'>): Promise<Game> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const newGame: Game = {\n          ...gameData,\n          id: Date.now().toString(),\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n        gamesDatabase.push(newGame);\n        resolve(newGame);\n      }, 100);\n    });\n  }\n\n  // 更新游戏\n  static async updateGame(id: string, updates: Partial<Game>): Promise<Game | null> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const index = gamesDatabase.findIndex(g => g.id === id);\n        if (index === -1) {\n          resolve(null);\n          return;\n        }\n\n        gamesDatabase[index] = {\n          ...gamesDatabase[index],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n        resolve(gamesDatabase[index]);\n      }, 100);\n    });\n  }\n\n  // 删除游戏\n  static async deleteGame(id: string): Promise<boolean> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const index = gamesDatabase.findIndex(g => g.id === id);\n        if (index === -1) {\n          resolve(false);\n          return;\n        }\n\n        gamesDatabase.splice(index, 1);\n        resolve(true);\n      }, 100);\n    });\n  }\n\n  // 获取游戏统计信息\n  static async getGameStats(): Promise<GameStats> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const stats: GameStats = {\n          totalGames: gamesDatabase.length,\n          genreDistribution: {},\n          platformDistribution: {},\n          yearDistribution: {},\n          averageRating: 0\n        };\n\n        // 计算各种分布\n        gamesDatabase.forEach(game => {\n          // 类型分布\n          game.genres.forEach(genre => {\n            stats.genreDistribution[genre.name] = (stats.genreDistribution[genre.name] || 0) + 1;\n          });\n\n          // 平台分布\n          game.platforms.forEach(platform => {\n            stats.platformDistribution[platform.name] = (stats.platformDistribution[platform.name] || 0) + 1;\n          });\n\n          // 年份分布\n          const year = new Date(game.releaseDate).getFullYear().toString();\n          stats.yearDistribution[year] = (stats.yearDistribution[year] || 0) + 1;\n        });\n\n        // 平均评分\n        const totalRatings = gamesDatabase.reduce((sum, game) => {\n          if (game.ratings.length > 0) {\n            return sum + game.ratings[0].score;\n          }\n          return sum;\n        }, 0);\n        stats.averageRating = totalRatings / gamesDatabase.length;\n\n        resolve(stats);\n      }, 100);\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,yBAAyB;AACzB,IAAI,gBAAwB;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa,EAAE;QACf,WAAW;QACX,WAAW;QACX,aAAa;QACb,WAAW;YAAC;gBAAE,IAAI;gBAAU,MAAM;gBAAmB,MAAM;YAAoB;SAAE;QACjF,QAAQ;YACN;gBAAE,IAAI;gBAAoB,MAAM;gBAAQ,OAAO;YAAU;YACzD;gBAAE,IAAI;gBAAc,MAAM;gBAAQ,OAAO;YAAU;SACpD;QACD,MAAM;YAAC;YAAQ;YAAM;YAAM;SAAK;QAChC,SAAS;YACP;gBAAE,QAAQ;gBAAO,OAAO;gBAAI,UAAU;gBAAI,WAAW;YAAkB;YACvE;gBAAE,QAAQ;gBAAY,OAAO;gBAAG,UAAU;YAAG;YAC7C;gBAAE,QAAQ;gBAAc,OAAO;gBAAI,UAAU;YAAI;SAClD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,cAAc;gBACd,MAAM;gBACN,UAAU;YACZ;SACD;QACD,UAAU;YAAC;YAAU;YAAQ;YAAQ;SAAO;QAC5C,WAAW;QACX,WAAW;QACX,QAAQ,uHAAA,CAAA,aAAU,CAAC,SAAS;IAC9B;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa,EAAE;QACf,WAAW;QACX,WAAW;QACX,aAAa;QACb,WAAW;YAAC;gBAAE,IAAI;gBAAO,MAAM;gBAAiB,MAAM;YAAiB;SAAE;QACzE,QAAQ;YACN;gBAAE,IAAI;gBAAoB,MAAM;gBAAQ,OAAO;YAAU;YACzD;gBAAE,IAAI;gBAAa,MAAM;gBAAM,OAAO;YAAU;SACjD;QACD,MAAM;YAAC;YAAM;YAAM;YAAM;SAAO;QAChC,SAAS;YACP;gBAAE,QAAQ;gBAAO,OAAO;gBAAI,UAAU;YAAG;YACzC;gBAAE,QAAQ;gBAAY,OAAO;gBAAG,UAAU;YAAG;YAC7C;gBAAE,QAAQ;gBAAc,OAAO;gBAAI,UAAU;YAAI;SAClD;QACD,QAAQ,EAAE;QACV,UAAU;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC1C,WAAW;QACX,WAAW;QACX,QAAQ,uHAAA,CAAA,aAAU,CAAC,SAAS;IAC9B;CACD;AAGM,MAAM;IACX,SAAS;IACT,aAAa,cAA+B;QAC1C,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW,IAAM,QAAQ;uBAAI;iBAAc,GAAG;QAChD;IACF;IAEA,WAAW;IACX,aAAa,YAAY,EAAU,EAAwB;QACzD,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,MAAM,OAAO,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9C,QAAQ,QAAQ;YAClB,GAAG;QACL;IACF;IAEA,UAAU;IACV,aAAa,YAAY,MAAkB,EAAmB;QAC5D,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,IAAI,gBAAgB;uBAAI;iBAAc;gBAEtC,OAAO;gBACP,IAAI,OAAO,MAAM,EAAE;oBACjB,MAAM,aAAa,OAAO,MAAM,CAAC,WAAW;oBAC5C,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAE1C;gBAEA,QAAQ;gBACR,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;oBAC7C,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,OAAO,MAAM,CAAE,QAAQ,CAAC,MAAM,EAAE;gBAE9D;gBAEA,QAAQ;gBACR,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,MAAM,GAAG,GAAG;oBACnD,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA,WAAY,OAAO,SAAS,CAAE,QAAQ,CAAC,SAAS,EAAE;gBAE1E;gBAEA,QAAQ;gBACR,IAAI,OAAO,SAAS,EAAE;oBACpB,gBAAgB,cAAc,MAAM,CAAC,CAAA;wBACnC,MAAM,OAAO,IAAI,KAAK,KAAK,WAAW,EAAE,WAAW;wBACnD,OAAO,QAAQ,OAAO,SAAS,AAAC,CAAC,EAAE,IAAI,QAAQ,OAAO,SAAS,AAAC,CAAC,EAAE;oBACrE;gBACF;gBAEA,KAAK;gBACL,IAAI,OAAO,MAAM,EAAE;oBACjB,cAAc,IAAI,CAAC,CAAC,GAAG;wBACrB,IAAI,aAAa;wBACjB,OAAQ,OAAO,MAAM;4BACnB,KAAK;gCACH,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;gCAC1C;4BACF,KAAK;gCACH,aAAa,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;gCAChF;4BACF,KAAK;gCACH,MAAM,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG;gCAC5D,MAAM,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG;gCAC5D,aAAa,UAAU;gCACvB;4BACF;gCACE,aAAa;wBACjB;wBACA,OAAO,OAAO,SAAS,KAAK,SAAS,CAAC,aAAa;oBACrD;gBACF;gBAEA,QAAQ;YACV,GAAG;QACL;IACF;IAEA,OAAO;IACP,aAAa,WAAW,QAAsD,EAAiB;QAC7F,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,MAAM,UAAgB;oBACpB,GAAG,QAAQ;oBACX,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,cAAc,IAAI,CAAC;gBACnB,QAAQ;YACV,GAAG;QACL;IACF;IAEA,OAAO;IACP,aAAa,WAAW,EAAU,EAAE,OAAsB,EAAwB;QAChF,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,MAAM,QAAQ,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpD,IAAI,UAAU,CAAC,GAAG;oBAChB,QAAQ;oBACR;gBACF;gBAEA,aAAa,CAAC,MAAM,GAAG;oBACrB,GAAG,aAAa,CAAC,MAAM;oBACvB,GAAG,OAAO;oBACV,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,QAAQ,aAAa,CAAC,MAAM;YAC9B,GAAG;QACL;IACF;IAEA,OAAO;IACP,aAAa,WAAW,EAAU,EAAoB;QACpD,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,MAAM,QAAQ,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpD,IAAI,UAAU,CAAC,GAAG;oBAChB,QAAQ;oBACR;gBACF;gBAEA,cAAc,MAAM,CAAC,OAAO;gBAC5B,QAAQ;YACV,GAAG;QACL;IACF;IAEA,WAAW;IACX,aAAa,eAAmC;QAC9C,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,MAAM,QAAmB;oBACvB,YAAY,cAAc,MAAM;oBAChC,mBAAmB,CAAC;oBACpB,sBAAsB,CAAC;oBACvB,kBAAkB,CAAC;oBACnB,eAAe;gBACjB;gBAEA,SAAS;gBACT,cAAc,OAAO,CAAC,CAAA;oBACpB,OAAO;oBACP,KAAK,MAAM,CAAC,OAAO,CAAC,CAAA;wBAClB,MAAM,iBAAiB,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,iBAAiB,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;oBACrF;oBAEA,OAAO;oBACP,KAAK,SAAS,CAAC,OAAO,CAAC,CAAA;wBACrB,MAAM,oBAAoB,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,MAAM,oBAAoB,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;oBACjG;oBAEA,OAAO;oBACP,MAAM,OAAO,IAAI,KAAK,KAAK,WAAW,EAAE,WAAW,GAAG,QAAQ;oBAC9D,MAAM,gBAAgB,CAAC,KAAK,GAAG,CAAC,MAAM,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI;gBACvE;gBAEA,OAAO;gBACP,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,KAAK;oBAC9C,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;wBAC3B,OAAO,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,KAAK;oBACpC;oBACA,OAAO;gBACT,GAAG;gBACH,MAAM,aAAa,GAAG,eAAe,cAAc,MAAM;gBAEzD,QAAQ;YACV,GAAG;QACL;IACF;AACF", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DebugGame/debuggame-website/src/components/GameBox.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Text, Box } from '@react-three/drei';\nimport * as THREE from 'three';\nimport { Game } from '@/types/game';\n\ninterface GameBoxProps {\n  game: Game & { position: [number, number, number] };\n  position: [number, number, number];\n}\n\nexport default function GameBox({ game, position }: GameBoxProps) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  const [clicked, setClicked] = useState(false);\n\n  // 动画效果\n  useFrame((state, delta) => {\n    if (meshRef.current) {\n      // 悬停时的浮动效果\n      if (hovered) {\n        meshRef.current.rotation.y += delta * 0.5;\n        meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n      } else {\n        meshRef.current.rotation.y += delta * 0.1;\n        meshRef.current.position.y = THREE.MathUtils.lerp(\n          meshRef.current.position.y,\n          position[1],\n          delta * 5\n        );\n      }\n      \n      // 点击时的缩放效果\n      const targetScale = clicked ? 1.2 : hovered ? 1.1 : 1;\n      meshRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), delta * 5);\n    }\n  });\n\n  const handleClick = () => {\n    setClicked(!clicked);\n    // 这里可以添加打开游戏详情的逻辑\n    console.log('Clicked game:', game.title);\n  };\n\n  return (\n    <group position={position}>\n      {/* 游戏盒子 */}\n      <Box\n        ref={meshRef}\n        args={[1, 1.4, 0.1]}\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n        onClick={handleClick}\n      >\n        {/* 盒子材质 */}\n        <meshStandardMaterial\n          color={hovered ? '#8b5cf6' : '#6366f1'}\n          metalness={0.3}\n          roughness={0.4}\n          emissive={hovered ? '#4c1d95' : '#000000'}\n          emissiveIntensity={hovered ? 0.2 : 0}\n        />\n      </Box>\n\n      {/* 游戏标题 */}\n      <Text\n        position={[0, -1, 0.1]}\n        fontSize={0.15}\n        color=\"white\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n        maxWidth={2}\n        textAlign=\"center\"\n      >\n        {game.title}\n      </Text>\n\n      {/* 评分显示 */}\n      {game.ratings.length > 0 && (\n        <Text\n          position={[0, -1.3, 0.1]}\n          fontSize={0.1}\n          color=\"#fbbf24\"\n          anchorX=\"center\"\n          anchorY=\"middle\"\n        >\n          ★ {(game.ratings[0].score / game.ratings[0].maxScore * 10).toFixed(1)}/10\n        </Text>\n      )}\n\n      {/* 平台信息 */}\n      <Text\n        position={[0, -1.5, 0.1]}\n        fontSize={0.08}\n        color=\"#94a3b8\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n      >\n        {game.platforms.map(p => p.name).join(', ')} • {new Date(game.releaseDate).getFullYear()}\n      </Text>\n\n      {/* 悬停时的光环效果 */}\n      {hovered && (\n        <mesh position={[0, 0, -0.1]}>\n          <ringGeometry args={[0.8, 1.0, 32]} />\n          <meshBasicMaterial\n            color=\"#8b5cf6\"\n            transparent\n            opacity={0.3}\n            side={THREE.DoubleSide}\n          />\n        </mesh>\n      )}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAae,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAgB;;IAC9D,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,OAAO;IACP,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;4BAAE,CAAC,OAAO;YACf,IAAI,QAAQ,OAAO,EAAE;gBACnB,WAAW;gBACX,IAAI,SAAS;oBACX,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,QAAQ;oBACtC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;gBACrF,OAAO;oBACL,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,QAAQ;oBACtC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,kJAAA,CAAA,YAAe,CAAC,IAAI,CAC/C,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,EAC1B,QAAQ,CAAC,EAAE,EACX,QAAQ;gBAEZ;gBAEA,WAAW;gBACX,MAAM,cAAc,UAAU,MAAM,UAAU,MAAM;gBACpD,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,kJAAA,CAAA,UAAa,CAAC,aAAa,aAAa,cAAc,QAAQ;YAC/F;QACF;;IAEA,MAAM,cAAc;QAClB,WAAW,CAAC;QACZ,kBAAkB;QAClB,QAAQ,GAAG,CAAC,iBAAiB,KAAK,KAAK;IACzC;IAEA,qBACE,6LAAC;QAAM,UAAU;;0BAEf,6LAAC,6JAAA,CAAA,MAAG;gBACF,KAAK;gBACL,MAAM;oBAAC;oBAAG;oBAAK;iBAAI;gBACnB,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;gBAC/B,SAAS;0BAGT,cAAA,6LAAC;oBACC,OAAO,UAAU,YAAY;oBAC7B,WAAW;oBACX,WAAW;oBACX,UAAU,UAAU,YAAY;oBAChC,mBAAmB,UAAU,MAAM;;;;;;;;;;;0BAKvC,6LAAC,2JAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAI;gBACtB,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,UAAU;gBACV,WAAU;0BAET,KAAK,KAAK;;;;;;YAIZ,KAAK,OAAO,CAAC,MAAM,GAAG,mBACrB,6LAAC,2JAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAI;gBACxB,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;;oBACT;oBACI,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,EAAE,OAAO,CAAC;oBAAG;;;;;;;0BAK1E,6LAAC,2JAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAI;gBACxB,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;;oBAEP,KAAK,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;oBAAM;oBAAI,IAAI,KAAK,KAAK,WAAW,EAAE,WAAW;;;;;;;YAIvF,yBACC,6LAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;;kCAC1B,6LAAC;wBAAa,MAAM;4BAAC;4BAAK;4BAAK;yBAAG;;;;;;kCAClC,6LAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,MAAM,kJAAA,CAAA,aAAgB;;;;;;;;;;;;;;;;;;AAMlC;GAxGwB;;QAMtB,kNAAA,CAAA,WAAQ;;;KANc", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DebugGame/debuggame-website/src/components/GameGallery.tsx"], "sourcesContent": ["'use client';\n\nimport { Canvas } from '@react-three/fiber';\nimport { OrbitControls } from '@react-three/drei';\nimport { Suspense, useState, useEffect } from 'react';\nimport { Game } from '@/types/game';\nimport { GameService } from '@/lib/gameService';\nimport GameBox from './GameBox';\n\n\n\nfunction Scene({ games }: { games: any[] }) {\n  return (\n    <>\n      <ambientLight intensity={0.6} />\n      <directionalLight position={[10, 10, 5]} intensity={1.2} />\n      <pointLight position={[-10, -10, -10]} intensity={0.8} />\n\n      {games.map((game) => (\n        <GameBox\n          key={game.id}\n          game={game}\n          position={game.position}\n        />\n      ))}\n\n      <OrbitControls\n        enablePan={true}\n        enableZoom={true}\n        enableRotate={true}\n        minDistance={3}\n        maxDistance={15}\n        autoRotate={true}\n        autoRotateSpeed={0.5}\n      />\n    </>\n  );\n}\n\nfunction LoadingFallback() {\n  return (\n    <div className=\"flex items-center justify-center h-96\">\n      <div className=\"text-white text-xl\">Loading 3D Gallery...</div>\n    </div>\n  );\n}\n\nexport default function GameGallery() {\n  const [games, setGames] = useState<Game[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const loadGames = async () => {\n      try {\n        const gamesData = await GameService.getAllGames();\n        // 为3D显示添加位置信息\n        const gamesWithPositions = gamesData.slice(0, 4).map((game, index) => ({\n          ...game,\n          position: [index * 2.5 - 3.75, 0, 0] // 均匀分布在X轴上\n        }));\n        setGames(gamesWithPositions);\n      } catch (error) {\n        console.error('Failed to load games:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadGames();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"w-full\">\n        <div className=\"mb-8\">\n          <h2 className=\"text-3xl font-bold text-white mb-4\">精选游戏</h2>\n          <p className=\"text-slate-300\">加载中...</p>\n        </div>\n        <div className=\"h-96 w-full rounded-lg overflow-hidden bg-slate-800/50 backdrop-blur-sm border border-white/10 flex items-center justify-center\">\n          <div className=\"text-white text-xl\">Loading 3D Gallery...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"mb-8\">\n        <h2 className=\"text-3xl font-bold text-white mb-4\">精选游戏</h2>\n        <p className=\"text-slate-300\">\n          使用鼠标拖拽旋转视角，滚轮缩放，点击游戏盒子查看详情\n        </p>\n      </div>\n      \n      <div className=\"h-96 w-full rounded-lg overflow-hidden bg-slate-800/50 backdrop-blur-sm border border-white/10\">\n        <Canvas\n          camera={{ position: [0, 2, 8], fov: 60 }}\n          gl={{ antialias: true, alpha: false }}\n          style={{ background: 'transparent' }}\n        >\n          <Suspense fallback={<LoadingFallback />}>\n            <Scene games={games} />\n          </Suspense>\n        </Canvas>\n      </div>\n\n      <div className=\"mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {games.map((game) => (\n          <div\n            key={game.id}\n            className=\"bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-colors\"\n          >\n            <h3 className=\"text-white font-semibold mb-2\">{game.title}</h3>\n            <div className=\"text-slate-300 text-sm space-y-1\">\n              <p>开发商: {game.developer}</p>\n              <p>发布: {new Date(game.releaseDate).getFullYear()}</p>\n              <p>平台: {game.platforms.map(p => p.name).join(', ')}</p>\n              {game.ratings.length > 0 && (\n                <p>评分: {(game.ratings[0].score / game.ratings[0].maxScore * 10).toFixed(1)}/10</p>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AAWA,SAAS,MAAM,EAAE,KAAK,EAAoB;IACxC,qBACE;;0BACE,6LAAC;gBAAa,WAAW;;;;;;0BACzB,6LAAC;gBAAiB,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBAAE,WAAW;;;;;;0BACpD,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI,CAAC;oBAAI,CAAC;iBAAG;gBAAE,WAAW;;;;;;YAEjD,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,gIAAA,CAAA,UAAO;oBAEN,MAAM;oBACN,UAAU,KAAK,QAAQ;mBAFlB,KAAK,EAAE;;;;;0BAMhB,6LAAC,oKAAA,CAAA,gBAAa;gBACZ,WAAW;gBACX,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,iBAAiB;;;;;;;;AAIzB;KA1BS;AA4BT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAAqB;;;;;;;;;;;AAG1C;MANS;AAQM,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;mDAAY;oBAChB,IAAI;wBACF,MAAM,YAAY,MAAM,4HAAA,CAAA,cAAW,CAAC,WAAW;wBAC/C,cAAc;wBACd,MAAM,qBAAqB,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG;kFAAC,CAAC,MAAM,QAAU,CAAC;oCACrE,GAAG,IAAI;oCACP,UAAU;wCAAC,QAAQ,MAAM;wCAAM;wCAAG;qCAAE,CAAC,WAAW;gCAClD,CAAC;;wBACD,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACzC,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;8BAEhC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI5C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;0BAKhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;oBACL,QAAQ;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,KAAK;oBAAG;oBACvC,IAAI;wBAAE,WAAW;wBAAM,OAAO;oBAAM;oBACpC,OAAO;wBAAE,YAAY;oBAAc;8BAEnC,cAAA,6LAAC,6JAAA,CAAA,WAAQ;wBAAC,wBAAU,6LAAC;;;;;kCACnB,cAAA,6LAAC;4BAAM,OAAO;;;;;;;;;;;;;;;;;;;;;0BAKpB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAiC,KAAK,KAAK;;;;;;0CACzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAE;4CAAM,KAAK,SAAS;;;;;;;kDACvB,6LAAC;;4CAAE;4CAAK,IAAI,KAAK,KAAK,WAAW,EAAE,WAAW;;;;;;;kDAC9C,6LAAC;;4CAAE;4CAAK,KAAK,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;;oCAC5C,KAAK,OAAO,CAAC,MAAM,GAAG,mBACrB,6LAAC;;4CAAE;4CAAK,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;uBAT1E,KAAK,EAAE;;;;;;;;;;;;;;;;AAiBxB;GA/EwB;MAAA", "debugId": null}}]}