'use client';

import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { Suspense, useState, useEffect } from 'react';
import { Game } from '@/types/game';
import { GameService } from '@/lib/gameService';
import GameBox from './GameBox';



function Scene({ games }: { games: any[] }) {
  return (
    <>
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1.2} />
      <pointLight position={[-10, -10, -10]} intensity={0.8} />

      {games.map((game) => (
        <GameBox
          key={game.id}
          game={game}
          position={game.position}
        />
      ))}

      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={3}
        maxDistance={15}
        autoRotate={true}
        autoRotateSpeed={0.5}
      />
    </>
  );
}

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center h-96">
      <div className="text-white text-xl">Loading 3D Gallery...</div>
    </div>
  );
}

export default function GameGallery() {
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadGames = async () => {
      try {
        const gamesData = await GameService.getAllGames();
        // 为3D显示添加位置信息
        const gamesWithPositions = gamesData.slice(0, 4).map((game, index) => ({
          ...game,
          position: [index * 2.5 - 3.75, 0, 0] // 均匀分布在X轴上
        }));
        setGames(gamesWithPositions);
      } catch (error) {
        console.error('Failed to load games:', error);
      } finally {
        setLoading(false);
      }
    };

    loadGames();
  }, []);

  if (loading) {
    return (
      <div className="w-full">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-4">精选游戏</h2>
          <p className="text-slate-300">加载中...</p>
        </div>
        <div className="h-96 w-full rounded-lg overflow-hidden bg-slate-800/50 backdrop-blur-sm border border-white/10 flex items-center justify-center">
          <div className="text-white text-xl">Loading 3D Gallery...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">精选游戏</h2>
        <p className="text-slate-300">
          使用鼠标拖拽旋转视角，滚轮缩放，点击游戏盒子查看详情
        </p>
      </div>
      
      <div className="h-96 w-full rounded-lg overflow-hidden bg-slate-800/50 backdrop-blur-sm border border-white/10">
        <Canvas
          camera={{ position: [0, 2, 8], fov: 60 }}
          gl={{ antialias: true, alpha: false }}
          style={{ background: 'transparent' }}
        >
          <Suspense fallback={<LoadingFallback />}>
            <Scene games={games} />
          </Suspense>
        </Canvas>
      </div>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {games.map((game) => (
          <div
            key={game.id}
            className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-colors"
          >
            <h3 className="text-white font-semibold mb-2">{game.title}</h3>
            <div className="text-slate-300 text-sm space-y-1">
              <p>开发商: {game.developer}</p>
              <p>发布: {new Date(game.releaseDate).getFullYear()}</p>
              <p>平台: {game.platforms.map(p => p.name).join(', ')}</p>
              {game.ratings.length > 0 && (
                <p>评分: {(game.ratings[0].score / game.ratings[0].maxScore * 10).toFixed(1)}/10</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
