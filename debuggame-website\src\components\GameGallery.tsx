'use client';

import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { Suspense } from 'react';
import GameBox from './GameBox';

// 示例游戏数据
const sampleGames = [
  {
    id: 1,
    title: 'The Legend of Zelda: Breath of the Wild',
    cover: '/game-covers/zelda-botw.jpg',
    platform: 'Nintendo Switch',
    year: 2017,
    rating: 9.7,
    position: [-4, 0, 0]
  },
  {
    id: 2,
    title: 'God of War',
    cover: '/game-covers/god-of-war.jpg',
    platform: 'PlayStation 4',
    year: 2018,
    rating: 9.5,
    position: [-1.5, 0, 0]
  },
  {
    id: 3,
    title: 'Red Dead Redemption 2',
    cover: '/game-covers/rdr2.jpg',
    platform: 'Multi-platform',
    year: 2018,
    rating: 9.3,
    position: [1, 0, 0]
  },
  {
    id: 4,
    title: 'The Witcher 3: Wild Hunt',
    cover: '/game-covers/witcher3.jpg',
    platform: 'Multi-platform',
    year: 2015,
    rating: 9.8,
    position: [3.5, 0, 0]
  }
];

function Scene() {
  return (
    <>
      <ambientLight intensity={0.4} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <pointLight position={[-10, -10, -10]} intensity={0.5} />
      
      {sampleGames.map((game) => (
        <GameBox
          key={game.id}
          game={game}
          position={game.position}
        />
      ))}
      
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={3}
        maxDistance={15}
        autoRotate={true}
        autoRotateSpeed={0.5}
      />
      <Environment preset="city" />
    </>
  );
}

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center h-96">
      <div className="text-white text-xl">Loading 3D Gallery...</div>
    </div>
  );
}

export default function GameGallery() {
  return (
    <div className="w-full">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">精选游戏</h2>
        <p className="text-slate-300">
          使用鼠标拖拽旋转视角，滚轮缩放，点击游戏盒子查看详情
        </p>
      </div>
      
      <div className="h-96 w-full rounded-lg overflow-hidden bg-black/20 backdrop-blur-sm border border-white/10">
        <Canvas
          camera={{ position: [0, 2, 8], fov: 60 }}
          gl={{ antialias: true, alpha: true }}
        >
          <Suspense fallback={null}>
            <Scene />
          </Suspense>
        </Canvas>
      </div>
      
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {sampleGames.map((game) => (
          <div
            key={game.id}
            className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-colors"
          >
            <h3 className="text-white font-semibold mb-2">{game.title}</h3>
            <div className="text-slate-300 text-sm space-y-1">
              <p>平台: {game.platform}</p>
              <p>年份: {game.year}</p>
              <p>评分: {game.rating}/10</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
