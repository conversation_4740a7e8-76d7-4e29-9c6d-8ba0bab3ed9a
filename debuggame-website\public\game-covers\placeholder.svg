<svg width="200" height="280" viewBox="0 0 200 280" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="280" fill="url(#gradient)"/>
  <rect x="10" y="10" width="180" height="260" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  <circle cx="100" cy="140" r="40" fill="rgba(255,255,255,0.1)"/>
  <path d="M80 130 L90 140 L80 150 M120 130 L110 140 L120 150 M90 120 L110 120 M90 160 L110 160" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-linecap="round"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4c1d95;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
