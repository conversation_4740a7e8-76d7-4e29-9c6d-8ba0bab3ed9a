'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Game, GameFilter, GameStats } from '@/types/game';
import { GameService } from '@/lib/gameService';
import GameList from '@/components/admin/GameList';
import GameForm from '@/components/admin/GameForm';
import GameStats as GameStatsComponent from '@/components/admin/GameStats';
import ArticleManagement from '@/components/admin/ArticleManagement';

export default function AdminPage() {
  const [games, setGames] = useState<Game[]>([]);
  const [stats, setStats] = useState<GameStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'list' | 'add' | 'stats'>('list');
  const [editingGame, setEditingGame] = useState<Game | null>(null);
  const [filter, setFilter] = useState<GameFilter>({});
  const [showArticleManagement, setShowArticleManagement] = useState(false);

  useEffect(() => {
    loadGames();
    loadStats();
  }, []);

  const loadGames = async () => {
    try {
      setLoading(true);
      const gamesData = await GameService.searchGames(filter);
      setGames(gamesData);
    } catch (error) {
      console.error('Failed to load games:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await GameService.getGameStats();
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleGameSave = async (gameData: any) => {
    try {
      if (editingGame) {
        await GameService.updateGame(editingGame.id, gameData);
      } else {
        await GameService.createGame(gameData);
      }
      await loadGames();
      await loadStats();
      setActiveTab('list');
      setEditingGame(null);
    } catch (error) {
      console.error('Failed to save game:', error);
    }
  };

  const handleGameDelete = async (id: string) => {
    if (confirm('确定要删除这个游戏吗？')) {
      try {
        await GameService.deleteGame(id);
        await loadGames();
        await loadStats();
      } catch (error) {
        console.error('Failed to delete game:', error);
      }
    }
  };

  const handleGameEdit = (game: Game) => {
    setEditingGame(game);
    setActiveTab('add');
  };

  const handleFilterChange = (newFilter: GameFilter) => {
    setFilter(newFilter);
  };

  useEffect(() => {
    loadGames();
  }, [filter]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                游戏管理后台
              </h1>
              <p className="text-slate-300">
                管理游戏信息、查看统计数据
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowArticleManagement(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                文章管理
              </button>
              <Link
                href="/"
                className="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors"
              >
                返回首页
              </Link>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-white/10 backdrop-blur-sm rounded-lg p-1">
            {[
              { key: 'list', label: '游戏列表', icon: '📋' },
              { key: 'add', label: editingGame ? '编辑游戏' : '添加游戏', icon: '➕' },
              { key: 'stats', label: '统计数据', icon: '📊' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => {
                  setActiveTab(tab.key as any);
                  if (tab.key !== 'add') setEditingGame(null);
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.key
                    ? 'bg-purple-600 text-white'
                    : 'text-slate-300 hover:text-white hover:bg-white/10'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
          {activeTab === 'list' && (
            <GameList
              games={games}
              loading={loading}
              onEdit={handleGameEdit}
              onDelete={handleGameDelete}
              onFilterChange={handleFilterChange}
              filter={filter}
            />
          )}

          {activeTab === 'add' && (
            <GameForm
              game={editingGame}
              onSave={handleGameSave}
              onCancel={() => {
                setActiveTab('list');
                setEditingGame(null);
              }}
            />
          )}

          {activeTab === 'stats' && stats && (
            <GameStatsComponent stats={stats} />
          )}
        </div>
      </div>

      {/* Article Management Modal */}
      {showArticleManagement && (
        <ArticleManagement onClose={() => setShowArticleManagement(false)} />
      )}
    </div>
  );
}
