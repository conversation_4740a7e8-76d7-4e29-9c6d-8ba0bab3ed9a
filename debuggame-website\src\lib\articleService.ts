import { 
  Article, 
  ArticleFilter, 
  ArticleStats, 
  ArticleCategory, 
  ArticleStatus,
  Author,
  AuthorRole,
  Comment
} from '@/types/article';

// 模拟数据存储
let articlesDatabase: Article[] = [
  {
    id: '1',
    title: '《塞尔达传说：旷野之息》完全攻略指南',
    content: `# 《塞尔达传说：旷野之息》完全攻略指南

## 游戏概述
《塞尔达传说：旷野之息》是任天堂开发的开放世界动作冒险游戏，为玩家提供了前所未有的自由度。

## 基础操作
- **移动**: 使用左摇杆控制林克移动
- **视角**: 使用右摇杆调整视角
- **跳跃**: 按X键跳跃
- **攀爬**: 靠近可攀爬表面按A键

## 重要系统详解

### 1. 料理系统
游戏中的料理系统是生存的关键：
- 收集各种食材
- 在篝火或锅子处进行烹饪
- 不同食材组合产生不同效果

### 2. 武器系统
- 武器会损坏，需要经常更换
- 不同武器有不同的攻击力和耐久度
- 学会合理分配武器使用

### 3. 神庙攻略
游戏中共有120个神庙，每个都有独特的谜题...`,
    excerpt: '详细介绍《塞尔达传说：旷野之息》的各个系统和攻略技巧，帮助新手玩家快速上手这款经典游戏。',
    coverImage: '/articles/zelda-guide-cover.jpg',
    author: {
      id: 'author1',
      name: '游戏达人小王',
      avatar: '/avatars/author1.jpg',
      bio: '资深游戏玩家，专注于动作冒险游戏攻略',
      role: AuthorRole.EDITOR
    },
    category: {
      id: 'guide',
      name: '游戏攻略',
      slug: 'guide',
      description: '详细的游戏攻略和技巧分享',
      color: '#10b981',
      icon: '📖'
    },
    tags: ['塞尔达传说', '攻略', '开放世界', '任天堂'],
    relatedGames: ['1'],
    status: ArticleStatus.PUBLISHED,
    publishedAt: '2024-01-15T10:00:00Z',
    views: 1250,
    likes: 89,
    comments: [
      {
        id: 'comment1',
        content: '非常详细的攻略，对新手很有帮助！',
        author: {
          id: 'user1',
          name: '游戏新手',
          avatar: '/avatars/user1.jpg'
        },
        createdAt: '2024-01-16T14:30:00Z',
        likes: 12
      }
    ],
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    slug: 'zelda-breath-of-wild-complete-guide',
    metaDescription: '《塞尔达传说：旷野之息》完整攻略指南，包含料理、武器、神庙等系统详解',
    keywords: ['塞尔达传说', '旷野之息', '攻略', '指南', '任天堂']
  },
  {
    id: '2',
    title: '战神4：父子情深的北欧神话之旅',
    content: `# 战神4：父子情深的北欧神话之旅

## 游戏背景
《战神4》标志着系列的重大转变，从希腊神话转向北欧神话，奎托斯与儿子阿特柔斯的关系成为故事核心。

## 故事分析
游戏讲述了一个关于成长、责任和救赎的故事...

## 战斗系统
- 利维坦之斧的使用技巧
- 符文攻击的搭配
- 阿特柔斯的辅助作用

## 探索要素
- 隐藏宝箱的位置
- 符文宝箱的解谜方法
- 奥丁乌鸦的收集攻略`,
    excerpt: '深度解析《战神4》的剧情内涵和游戏机制，探讨父子关系在游戏中的重要意义。',
    coverImage: '/articles/god-of-war-analysis.jpg',
    author: {
      id: 'author2',
      name: '游戏评论家',
      avatar: '/avatars/author2.jpg',
      bio: '专业游戏评论员，关注游戏的艺术价值',
      role: AuthorRole.CONTRIBUTOR
    },
    category: {
      id: 'analysis',
      name: '游戏分析',
      slug: 'analysis',
      description: '深度分析游戏的设计理念和艺术价值',
      color: '#8b5cf6',
      icon: '🔍'
    },
    tags: ['战神', '剧情分析', '北欧神话', 'PlayStation'],
    relatedGames: ['2'],
    status: ArticleStatus.PUBLISHED,
    publishedAt: '2024-01-20T15:00:00Z',
    views: 890,
    likes: 67,
    comments: [],
    createdAt: '2024-01-20T14:00:00Z',
    updatedAt: '2024-01-20T15:00:00Z',
    slug: 'god-of-war-4-story-analysis',
    metaDescription: '深度分析《战神4》的剧情设计和父子关系主题',
    keywords: ['战神', '剧情分析', '北欧神话', '游戏评论']
  }
];

let categoriesDatabase: ArticleCategory[] = [
  {
    id: 'guide',
    name: '游戏攻略',
    slug: 'guide',
    description: '详细的游戏攻略和技巧分享',
    color: '#10b981',
    icon: '📖'
  },
  {
    id: 'analysis',
    name: '游戏分析',
    slug: 'analysis',
    description: '深度分析游戏的设计理念和艺术价值',
    color: '#8b5cf6',
    icon: '🔍'
  },
  {
    id: 'review',
    name: '游戏评测',
    slug: 'review',
    description: '客观公正的游戏评测和推荐',
    color: '#f59e0b',
    icon: '⭐'
  },
  {
    id: 'news',
    name: '游戏资讯',
    slug: 'news',
    description: '最新的游戏行业动态和新闻',
    color: '#ef4444',
    icon: '📰'
  }
];

// 文章管理服务类
export class ArticleService {
  // 获取所有文章
  static async getAllArticles(): Promise<Article[]> {
    return new Promise((resolve) => {
      setTimeout(() => resolve([...articlesDatabase]), 100);
    });
  }

  // 根据ID获取文章
  static async getArticleById(id: string): Promise<Article | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const article = articlesDatabase.find(a => a.id === id);
        resolve(article || null);
      }, 100);
    });
  }

  // 根据slug获取文章
  static async getArticleBySlug(slug: string): Promise<Article | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const article = articlesDatabase.find(a => a.slug === slug);
        resolve(article || null);
      }, 100);
    });
  }

  // 搜索和筛选文章
  static async searchArticles(filter: ArticleFilter): Promise<Article[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredArticles = [...articlesDatabase];

        // 只显示已发布的文章（除非指定状态）
        if (!filter.status) {
          filteredArticles = filteredArticles.filter(article => 
            article.status === ArticleStatus.PUBLISHED
          );
        } else {
          filteredArticles = filteredArticles.filter(article => 
            article.status === filter.status
          );
        }

        // 搜索标题和内容
        if (filter.search) {
          const searchTerm = filter.search.toLowerCase();
          filteredArticles = filteredArticles.filter(article =>
            article.title.toLowerCase().includes(searchTerm) ||
            article.content.toLowerCase().includes(searchTerm) ||
            article.excerpt.toLowerCase().includes(searchTerm)
          );
        }

        // 按分类筛选
        if (filter.category) {
          filteredArticles = filteredArticles.filter(article =>
            article.category.id === filter.category
          );
        }

        // 按标签筛选
        if (filter.tags && filter.tags.length > 0) {
          filteredArticles = filteredArticles.filter(article =>
            filter.tags!.some(tag => article.tags.includes(tag))
          );
        }

        // 按作者筛选
        if (filter.author) {
          filteredArticles = filteredArticles.filter(article =>
            article.author.id === filter.author
          );
        }

        // 按关联游戏筛选
        if (filter.relatedGame) {
          filteredArticles = filteredArticles.filter(article =>
            article.relatedGames.includes(filter.relatedGame!)
          );
        }

        // 排序
        if (filter.sortBy) {
          filteredArticles.sort((a, b) => {
            let comparison = 0;
            switch (filter.sortBy) {
              case 'title':
                comparison = a.title.localeCompare(b.title);
                break;
              case 'createdAt':
                comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
                break;
              case 'publishedAt':
                const aDate = a.publishedAt ? new Date(a.publishedAt).getTime() : 0;
                const bDate = b.publishedAt ? new Date(b.publishedAt).getTime() : 0;
                comparison = aDate - bDate;
                break;
              case 'views':
                comparison = a.views - b.views;
                break;
              case 'likes':
                comparison = a.likes - b.likes;
                break;
              default:
                comparison = 0;
            }
            return filter.sortOrder === 'desc' ? -comparison : comparison;
          });
        }

        // 分页
        if (filter.offset || filter.limit) {
          const offset = filter.offset || 0;
          const limit = filter.limit || filteredArticles.length;
          filteredArticles = filteredArticles.slice(offset, offset + limit);
        }

        resolve(filteredArticles);
      }, 200);
    });
  }

  // 创建文章
  static async createArticle(articleData: Omit<Article, 'id' | 'createdAt' | 'updatedAt' | 'views' | 'likes' | 'comments'>): Promise<Article> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newArticle: Article = {
          ...articleData,
          id: Date.now().toString(),
          views: 0,
          likes: 0,
          comments: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        articlesDatabase.push(newArticle);
        resolve(newArticle);
      }, 100);
    });
  }

  // 更新文章
  static async updateArticle(id: string, updates: Partial<Article>): Promise<Article | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = articlesDatabase.findIndex(a => a.id === id);
        if (index === -1) {
          resolve(null);
          return;
        }

        articlesDatabase[index] = {
          ...articlesDatabase[index],
          ...updates,
          updatedAt: new Date().toISOString()
        };
        resolve(articlesDatabase[index]);
      }, 100);
    });
  }

  // 删除文章
  static async deleteArticle(id: string): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = articlesDatabase.findIndex(a => a.id === id);
        if (index === -1) {
          resolve(false);
          return;
        }

        articlesDatabase.splice(index, 1);
        resolve(true);
      }, 100);
    });
  }

  // 获取文章分类
  static async getCategories(): Promise<ArticleCategory[]> {
    return new Promise((resolve) => {
      setTimeout(() => resolve([...categoriesDatabase]), 100);
    });
  }

  // 获取文章统计
  static async getArticleStats(): Promise<ArticleStats> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const publishedArticles = articlesDatabase.filter(a => a.status === ArticleStatus.PUBLISHED);
        
        const stats: ArticleStats = {
          totalArticles: articlesDatabase.length,
          publishedArticles: publishedArticles.length,
          draftArticles: articlesDatabase.filter(a => a.status === ArticleStatus.DRAFT).length,
          totalViews: articlesDatabase.reduce((sum, a) => sum + a.views, 0),
          totalLikes: articlesDatabase.reduce((sum, a) => sum + a.likes, 0),
          totalComments: articlesDatabase.reduce((sum, a) => sum + a.comments.length, 0),
          categoryDistribution: {},
          authorStats: {},
          popularTags: [],
          monthlyStats: []
        };

        // 分类分布
        articlesDatabase.forEach(article => {
          stats.categoryDistribution[article.category.name] = 
            (stats.categoryDistribution[article.category.name] || 0) + 1;
        });

        // 作者统计
        articlesDatabase.forEach(article => {
          stats.authorStats[article.author.name] = 
            (stats.authorStats[article.author.name] || 0) + 1;
        });

        // 热门标签
        const tagCounts: { [key: string]: number } = {};
        articlesDatabase.forEach(article => {
          article.tags.forEach(tag => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          });
        });
        stats.popularTags = Object.entries(tagCounts)
          .map(([tag, count]) => ({ tag, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 10);

        resolve(stats);
      }, 100);
    });
  }

  // 增加文章浏览量
  static async incrementViews(id: string): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const article = articlesDatabase.find(a => a.id === id);
        if (article) {
          article.views += 1;
        }
        resolve();
      }, 50);
    });
  }

  // 点赞文章
  static async likeArticle(id: string): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const article = articlesDatabase.find(a => a.id === id);
        if (article) {
          article.likes += 1;
          resolve(true);
        } else {
          resolve(false);
        }
      }, 50);
    });
  }
}
