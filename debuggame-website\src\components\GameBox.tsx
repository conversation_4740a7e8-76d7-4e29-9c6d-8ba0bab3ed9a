'use client';

import { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Text, Box } from '@react-three/drei';
import * as THREE from 'three';
import { Game } from '@/types/game';

interface GameBoxProps {
  game: Game & { position: [number, number, number] };
  position: [number, number, number];
}

export default function GameBox({ game, position }: GameBoxProps) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const [clicked, setClicked] = useState(false);

  // 动画效果
  useFrame((state, delta) => {
    if (meshRef.current) {
      // 悬停时的浮动效果
      if (hovered) {
        meshRef.current.rotation.y += delta * 0.5;
        meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;
      } else {
        meshRef.current.rotation.y += delta * 0.1;
        meshRef.current.position.y = THREE.MathUtils.lerp(
          meshRef.current.position.y,
          position[1],
          delta * 5
        );
      }
      
      // 点击时的缩放效果
      const targetScale = clicked ? 1.2 : hovered ? 1.1 : 1;
      meshRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), delta * 5);
    }
  });

  const handleClick = () => {
    setClicked(!clicked);
    // 这里可以添加打开游戏详情的逻辑
    console.log('Clicked game:', game.title);
  };

  return (
    <group position={position}>
      {/* 游戏盒子 */}
      <Box
        ref={meshRef}
        args={[1, 1.4, 0.1]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        onClick={handleClick}
      >
        {/* 盒子材质 */}
        <meshStandardMaterial
          color={hovered ? '#8b5cf6' : '#6366f1'}
          metalness={0.3}
          roughness={0.4}
          emissive={hovered ? '#4c1d95' : '#000000'}
          emissiveIntensity={hovered ? 0.2 : 0}
        />
      </Box>

      {/* 游戏标题 */}
      <Text
        position={[0, -1, 0.1]}
        fontSize={0.15}
        color="white"
        anchorX="center"
        anchorY="middle"
        maxWidth={2}
        textAlign="center"
      >
        {game.title}
      </Text>

      {/* 评分显示 */}
      {game.ratings.length > 0 && (
        <Text
          position={[0, -1.3, 0.1]}
          fontSize={0.1}
          color="#fbbf24"
          anchorX="center"
          anchorY="middle"
        >
          ★ {(game.ratings[0].score / game.ratings[0].maxScore * 10).toFixed(1)}/10
        </Text>
      )}

      {/* 平台信息 */}
      <Text
        position={[0, -1.5, 0.1]}
        fontSize={0.08}
        color="#94a3b8"
        anchorX="center"
        anchorY="middle"
      >
        {game.platforms.map(p => p.name).join(', ')} • {new Date(game.releaseDate).getFullYear()}
      </Text>

      {/* 悬停时的光环效果 */}
      {hovered && (
        <mesh position={[0, 0, -0.1]}>
          <ringGeometry args={[0.8, 1.0, 32]} />
          <meshBasicMaterial
            color="#8b5cf6"
            transparent
            opacity={0.3}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}
    </group>
  );
}
