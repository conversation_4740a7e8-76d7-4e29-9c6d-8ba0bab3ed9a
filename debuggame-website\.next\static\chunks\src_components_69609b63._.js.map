{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DebugGame/debuggame-website/src/components/GameBox.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame, useLoader } from '@react-three/fiber';\nimport { Text, Box } from '@react-three/drei';\nimport * as THREE from 'three';\n\ninterface Game {\n  id: number;\n  title: string;\n  cover: string;\n  platform: string;\n  year: number;\n  rating: number;\n  position: [number, number, number];\n}\n\ninterface GameBoxProps {\n  game: Game;\n  position: [number, number, number];\n}\n\nexport default function GameBox({ game, position }: GameBoxProps) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  const [clicked, setClicked] = useState(false);\n\n  // 动画效果\n  useFrame((state, delta) => {\n    if (meshRef.current) {\n      // 悬停时的浮动效果\n      if (hovered) {\n        meshRef.current.rotation.y += delta * 0.5;\n        meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n      } else {\n        meshRef.current.rotation.y += delta * 0.1;\n        meshRef.current.position.y = THREE.MathUtils.lerp(\n          meshRef.current.position.y,\n          position[1],\n          delta * 5\n        );\n      }\n      \n      // 点击时的缩放效果\n      const targetScale = clicked ? 1.2 : hovered ? 1.1 : 1;\n      meshRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), delta * 5);\n    }\n  });\n\n  const handleClick = () => {\n    setClicked(!clicked);\n    // 这里可以添加打开游戏详情的逻辑\n    console.log('Clicked game:', game.title);\n  };\n\n  return (\n    <group position={position}>\n      {/* 游戏盒子 */}\n      <Box\n        ref={meshRef}\n        args={[1, 1.4, 0.1]}\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n        onClick={handleClick}\n      >\n        {/* 盒子材质 */}\n        <meshStandardMaterial\n          color={hovered ? '#8b5cf6' : '#6366f1'}\n          metalness={0.3}\n          roughness={0.4}\n          emissive={hovered ? '#4c1d95' : '#000000'}\n          emissiveIntensity={hovered ? 0.2 : 0}\n        />\n      </Box>\n\n      {/* 游戏标题 */}\n      <Text\n        position={[0, -1, 0.1]}\n        fontSize={0.15}\n        color=\"white\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n        maxWidth={2}\n        textAlign=\"center\"\n      >\n        {game.title}\n      </Text>\n\n      {/* 评分显示 */}\n      <Text\n        position={[0, -1.3, 0.1]}\n        fontSize={0.1}\n        color=\"#fbbf24\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n      >\n        ★ {game.rating}/10\n      </Text>\n\n      {/* 平台信息 */}\n      <Text\n        position={[0, -1.5, 0.1]}\n        fontSize={0.08}\n        color=\"#94a3b8\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n      >\n        {game.platform} • {game.year}\n      </Text>\n\n      {/* 悬停时的光环效果 */}\n      {hovered && (\n        <mesh position={[0, 0, -0.1]}>\n          <ringGeometry args={[0.8, 1.0, 32]} />\n          <meshBasicMaterial\n            color=\"#8b5cf6\"\n            transparent\n            opacity={0.3}\n            side={THREE.DoubleSide}\n          />\n        </mesh>\n      )}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAsBe,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAgB;;IAC9D,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,OAAO;IACP,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;4BAAE,CAAC,OAAO;YACf,IAAI,QAAQ,OAAO,EAAE;gBACnB,WAAW;gBACX,IAAI,SAAS;oBACX,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,QAAQ;oBACtC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;gBACrF,OAAO;oBACL,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,QAAQ;oBACtC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,kJAAA,CAAA,YAAe,CAAC,IAAI,CAC/C,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,EAC1B,QAAQ,CAAC,EAAE,EACX,QAAQ;gBAEZ;gBAEA,WAAW;gBACX,MAAM,cAAc,UAAU,MAAM,UAAU,MAAM;gBACpD,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,kJAAA,CAAA,UAAa,CAAC,aAAa,aAAa,cAAc,QAAQ;YAC/F;QACF;;IAEA,MAAM,cAAc;QAClB,WAAW,CAAC;QACZ,kBAAkB;QAClB,QAAQ,GAAG,CAAC,iBAAiB,KAAK,KAAK;IACzC;IAEA,qBACE,6LAAC;QAAM,UAAU;;0BAEf,6LAAC,6JAAA,CAAA,MAAG;gBACF,KAAK;gBACL,MAAM;oBAAC;oBAAG;oBAAK;iBAAI;gBACnB,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;gBAC/B,SAAS;0BAGT,cAAA,6LAAC;oBACC,OAAO,UAAU,YAAY;oBAC7B,WAAW;oBACX,WAAW;oBACX,UAAU,UAAU,YAAY;oBAChC,mBAAmB,UAAU,MAAM;;;;;;;;;;;0BAKvC,6LAAC,2JAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAI;gBACtB,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,UAAU;gBACV,WAAU;0BAET,KAAK,KAAK;;;;;;0BAIb,6LAAC,2JAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAI;gBACxB,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;;oBACT;oBACI,KAAK,MAAM;oBAAC;;;;;;;0BAIjB,6LAAC,2JAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAI;gBACxB,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;;oBAEP,KAAK,QAAQ;oBAAC;oBAAI,KAAK,IAAI;;;;;;;YAI7B,yBACC,6LAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;;kCAC1B,6LAAC;wBAAa,MAAM;4BAAC;4BAAK;4BAAK;yBAAG;;;;;;kCAClC,6LAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,MAAM,kJAAA,CAAA,aAAgB;;;;;;;;;;;;;;;;;;AAMlC;GAtGwB;;QAMtB,kNAAA,CAAA,WAAQ;;;KANc", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DebugGame/debuggame-website/src/components/GameGallery.tsx"], "sourcesContent": ["'use client';\n\nimport { Canvas } from '@react-three/fiber';\nimport { OrbitControls } from '@react-three/drei';\nimport { Suspense, useState, useEffect } from 'react';\nimport { Game } from '@/types/game';\nimport { GameService } from '@/lib/gameService';\nimport GameBox from './GameBox';\n\n// 示例游戏数据\nconst sampleGames = [\n  {\n    id: 1,\n    title: 'The Legend of Zelda: Breath of the Wild',\n    cover: '/game-covers/zelda-botw.jpg',\n    platform: 'Nintendo Switch',\n    year: 2017,\n    rating: 9.7,\n    position: [-4, 0, 0]\n  },\n  {\n    id: 2,\n    title: 'God of War',\n    cover: '/game-covers/god-of-war.jpg',\n    platform: 'PlayStation 4',\n    year: 2018,\n    rating: 9.5,\n    position: [-1.5, 0, 0]\n  },\n  {\n    id: 3,\n    title: 'Red Dead Redemption 2',\n    cover: '/game-covers/rdr2.jpg',\n    platform: 'Multi-platform',\n    year: 2018,\n    rating: 9.3,\n    position: [1, 0, 0]\n  },\n  {\n    id: 4,\n    title: 'The Witcher 3: Wild Hunt',\n    cover: '/game-covers/witcher3.jpg',\n    platform: 'Multi-platform',\n    year: 2015,\n    rating: 9.8,\n    position: [3.5, 0, 0]\n  }\n];\n\nfunction Scene() {\n  return (\n    <>\n      <ambientLight intensity={0.6} />\n      <directionalLight position={[10, 10, 5]} intensity={1.2} />\n      <pointLight position={[-10, -10, -10]} intensity={0.8} />\n\n      {sampleGames.map((game) => (\n        <GameBox\n          key={game.id}\n          game={game}\n          position={game.position}\n        />\n      ))}\n\n      <OrbitControls\n        enablePan={true}\n        enableZoom={true}\n        enableRotate={true}\n        minDistance={3}\n        maxDistance={15}\n        autoRotate={true}\n        autoRotateSpeed={0.5}\n      />\n    </>\n  );\n}\n\nfunction LoadingFallback() {\n  return (\n    <div className=\"flex items-center justify-center h-96\">\n      <div className=\"text-white text-xl\">Loading 3D Gallery...</div>\n    </div>\n  );\n}\n\nexport default function GameGallery() {\n  return (\n    <div className=\"w-full\">\n      <div className=\"mb-8\">\n        <h2 className=\"text-3xl font-bold text-white mb-4\">精选游戏</h2>\n        <p className=\"text-slate-300\">\n          使用鼠标拖拽旋转视角，滚轮缩放，点击游戏盒子查看详情\n        </p>\n      </div>\n      \n      <div className=\"h-96 w-full rounded-lg overflow-hidden bg-slate-800/50 backdrop-blur-sm border border-white/10\">\n        <Canvas\n          camera={{ position: [0, 2, 8], fov: 60 }}\n          gl={{ antialias: true, alpha: false }}\n          style={{ background: 'transparent' }}\n        >\n          <Suspense fallback={<LoadingFallback />}>\n            <Scene />\n          </Suspense>\n        </Canvas>\n      </div>\n      \n      <div className=\"mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {sampleGames.map((game) => (\n          <div\n            key={game.id}\n            className=\"bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-colors\"\n          >\n            <h3 className=\"text-white font-semibold mb-2\">{game.title}</h3>\n            <div className=\"text-slate-300 text-sm space-y-1\">\n              <p>平台: {game.platform}</p>\n              <p>年份: {game.year}</p>\n              <p>评分: {game.rating}/10</p>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AAPA;;;;;;AASA,SAAS;AACT,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,UAAU;YAAC,CAAC;YAAG;YAAG;SAAE;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,UAAU;YAAC,CAAC;YAAK;YAAG;SAAE;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAG;YAAG;SAAE;IACrB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAK;YAAG;SAAE;IACvB;CACD;AAED,SAAS;IACP,qBACE;;0BACE,6LAAC;gBAAa,WAAW;;;;;;0BACzB,6LAAC;gBAAiB,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBAAE,WAAW;;;;;;0BACpD,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI,CAAC;oBAAI,CAAC;iBAAG;gBAAE,WAAW;;;;;;YAEjD,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,gIAAA,CAAA,UAAO;oBAEN,MAAM;oBACN,UAAU,KAAK,QAAQ;mBAFlB,KAAK,EAAE;;;;;0BAMhB,6LAAC,oKAAA,CAAA,gBAAa;gBACZ,WAAW;gBACX,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,iBAAiB;;;;;;;;AAIzB;KA1BS;AA4BT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAAqB;;;;;;;;;;;AAG1C;MANS;AAQM,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;0BAKhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;oBACL,QAAQ;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,KAAK;oBAAG;oBACvC,IAAI;wBAAE,WAAW;wBAAM,OAAO;oBAAM;oBACpC,OAAO;wBAAE,YAAY;oBAAc;8BAEnC,cAAA,6LAAC,6JAAA,CAAA,WAAQ;wBAAC,wBAAU,6LAAC;;;;;kCACnB,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAiC,KAAK,KAAK;;;;;;0CACzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAE;4CAAK,KAAK,QAAQ;;;;;;;kDACrB,6LAAC;;4CAAE;4CAAK,KAAK,IAAI;;;;;;;kDACjB,6LAAC;;4CAAE;4CAAK,KAAK,MAAM;4CAAC;;;;;;;;;;;;;;uBAPjB,KAAK,EAAE;;;;;;;;;;;;;;;;AAcxB;MAvCwB", "debugId": null}}]}