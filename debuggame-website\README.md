# DebugGame Website

一个专注于游戏回归本质的综合性游戏社区网站，采用现代化的3D展示技术和社区功能。

## 🎯 项目概述

DebugGame 旨在创建一个回归游戏本质的平台，通过3D可视化展示、深度游戏分析、社区讨论等功能，让玩家重新发现游戏的艺术价值。

## 🚀 已完成功能

### ✅ 项目初始化与基础环境
- Next.js 15 + TypeScript + Tailwind CSS
- React Three Fiber 3D渲染引擎
- 响应式设计基础架构

### ✅ 3D游戏展示系统（基础版）
- 3D游戏盒子展示效果
- 鼠标交互（悬停、点击、旋转）
- 自动旋转和动画效果
- 游戏信息展示（标题、评分、平台）

## 🛠️ 技术栈

- **前端框架**: Next.js 15 (React 19 + TypeScript)
- **样式**: Tailwind CSS
- **3D渲染**: React Three Fiber + Three.js + @react-three/drei
- **开发工具**: ESLint + Turbopack

## 🏃‍♂️ 快速开始

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📋 下一步开发计划

### 🔄 当前进行中
- 3D游戏展示系统优化
- 游戏封面纹理加载
- 交互动画完善

### 📅 待开发功能
1. **游戏信息管理系统** - 游戏数据录入和管理
2. **评分与获奖信息集成** - 各大媒体评分API集成
3. **游戏系统拆分分析** - 游戏机制深度分析工具
4. **攻略文章系统** - 富文本编辑和文章管理
5. **社区功能** - 用户系统、讨论区、评价功能
6. **DOS游戏模拟器** - 在线老游戏运行功能
7. **响应式优化** - 移动端适配和性能优化
8. **数据库设计** - 后端API和数据存储
9. **测试与部署** - 单元测试和生产环境部署

## 🎮 功能特色

- **3D游戏展示**: 每个游戏以3D盒子形式展示，支持交互操作
- **沉浸式体验**: 渐变背景、动画效果、响应式设计
- **游戏信息聚合**: 评分、获奖、平台等信息一站式展示
- **社区导向**: 专注游戏本质讨论，避免商业炒作

## 📁 项目结构

```
debuggame-website/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── layout.tsx       # 根布局
│   │   ├── page.tsx         # 首页
│   │   └── globals.css      # 全局样式
│   └── components/          # React组件
│       ├── GameGallery.tsx  # 游戏画廊主组件
│       └── GameBox.tsx      # 3D游戏盒子组件
├── public/                  # 静态资源
│   └── game-covers/         # 游戏封面图片
└── ...配置文件
```

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目！

## 📄 许可证

MIT License
