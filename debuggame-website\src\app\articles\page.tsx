'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Article, ArticleFilter, ArticleCategory } from '@/types/article';
import { ArticleService } from '@/lib/articleService';

export default function ArticlesPage() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<ArticleCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<ArticleFilter>({
    sortBy: 'publishedAt',
    sortOrder: 'desc'
  });

  useEffect(() => {
    loadArticles();
    loadCategories();
  }, []);

  useEffect(() => {
    loadArticles();
  }, [filter]);

  const loadArticles = async () => {
    try {
      setLoading(true);
      const articlesData = await ArticleService.searchArticles(filter);
      setArticles(articlesData);
    } catch (error) {
      console.error('Failed to load articles:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await ArticleService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const handleSearch = (searchTerm: string) => {
    setFilter(prev => ({ ...prev, search: searchTerm }));
  };

  const handleCategoryFilter = (categoryId: string) => {
    setFilter(prev => ({ 
      ...prev, 
      category: prev.category === categoryId ? undefined : categoryId 
    }));
  };

  const handleSortChange = (sortBy: string) => {
    setFilter(prev => ({
      ...prev,
      sortBy: sortBy as any,
      sortOrder: prev.sortBy === sortBy && prev.sortOrder === 'desc' ? 'asc' : 'desc'
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                攻略文章
              </h1>
              <p className="text-slate-300">
                深度攻略、游戏分析、评测推荐
              </p>
            </div>
            <Link
              href="/"
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              返回首页
            </Link>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="mb-8 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="搜索文章标题、内容..."
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Sort */}
            <div className="flex gap-2">
              <select
                value={filter.sortBy || ''}
                onChange={(e) => handleSortChange(e.target.value)}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="publishedAt">按发布时间</option>
                <option value="views">按浏览量</option>
                <option value="likes">按点赞数</option>
                <option value="title">按标题</option>
              </select>
            </div>
          </div>

          {/* Categories */}
          <div className="mt-4">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleCategoryFilter('')}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  !filter.category
                    ? 'bg-purple-600 text-white'
                    : 'bg-white/10 text-slate-300 hover:bg-white/20'
                }`}
              >
                全部
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategoryFilter(category.id)}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    filter.category === category.id
                      ? 'text-white'
                      : 'bg-white/10 text-slate-300 hover:bg-white/20'
                  }`}
                  style={{
                    backgroundColor: filter.category === category.id ? category.color : undefined
                  }}
                >
                  {category.icon} {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Articles Grid */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-white text-xl">加载中...</div>
          </div>
        ) : articles.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-slate-400 text-lg">没有找到文章</div>
            <p className="text-slate-500 mt-2">尝试调整搜索条件</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {articles.map((article) => (
              <ArticleCard key={article.id} article={article} />
            ))}
          </div>
        )}

        {/* Results Summary */}
        {!loading && (
          <div className="mt-8 text-center text-slate-400 text-sm">
            显示 {articles.length} 篇文章
          </div>
        )}
      </div>
    </div>
  );
}

interface ArticleCardProps {
  article: Article;
}

function ArticleCard({ article }: ArticleCardProps) {
  return (
    <Link href={`/articles/${article.slug}`}>
      <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden hover:bg-white/10 transition-all duration-300 hover:scale-105">
        {/* Cover Image */}
        <div className="aspect-video bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
          <div className="text-white text-4xl">{article.category.icon}</div>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Category */}
          <div className="mb-2">
            <span
              className="inline-block px-2 py-1 rounded text-xs font-medium"
              style={{ 
                backgroundColor: `${article.category.color}20`, 
                color: article.category.color 
              }}
            >
              {article.category.name}
            </span>
          </div>

          {/* Title */}
          <h3 className="text-white font-semibold text-lg mb-2 line-clamp-2">
            {article.title}
          </h3>

          {/* Excerpt */}
          <p className="text-slate-300 text-sm mb-3 line-clamp-3">
            {article.excerpt}
          </p>

          {/* Meta Info */}
          <div className="flex items-center justify-between text-xs text-slate-400">
            <div className="flex items-center gap-2">
              <span>{article.author.name}</span>
              <span>•</span>
              <span>{new Date(article.publishedAt!).toLocaleDateString('zh-CN')}</span>
            </div>
            <div className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                👁️ {article.views}
              </span>
              <span className="flex items-center gap-1">
                ❤️ {article.likes}
              </span>
            </div>
          </div>

          {/* Tags */}
          <div className="mt-3 flex flex-wrap gap-1">
            {article.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-white/10 text-slate-300 rounded text-xs"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    </Link>
  );
}
