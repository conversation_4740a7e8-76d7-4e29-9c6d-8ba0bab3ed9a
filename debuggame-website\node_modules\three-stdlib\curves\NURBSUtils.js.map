{"version": 3, "file": "NURBSUtils.js", "sources": ["../../src/curves/NURBSUtils.js"], "sourcesContent": ["import { Vector3, Vector4 } from 'three'\n\n/**\n * NURBS utils\n *\n * See NURBSCurve and NURBSSurface.\n **/\n\n/**************************************************************\n *\tNURBS Utils\n **************************************************************/\n\n/*\nFinds knot vector span.\n\np : degree\nu : parametric value\nU : knot vector\n\nreturns the span\n*/\nfunction findSpan(p, u, U) {\n  const n = U.length - p - 1\n\n  if (u >= U[n]) {\n    return n - 1\n  }\n\n  if (u <= U[p]) {\n    return p\n  }\n\n  let low = p\n  let high = n\n  let mid = Math.floor((low + high) / 2)\n\n  while (u < U[mid] || u >= U[mid + 1]) {\n    if (u < U[mid]) {\n      high = mid\n    } else {\n      low = mid\n    }\n\n    mid = Math.floor((low + high) / 2)\n  }\n\n  return mid\n}\n\n/*\nCalculate basis functions. See The NURBS Book, page 70, algorithm A2.2\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nU    : knot vector\n\nreturns array[p+1] with basis functions values.\n*/\nfunction calcBasisFunctions(span, u, p, U) {\n  const N = []\n  const left = []\n  const right = []\n  N[0] = 1.0\n\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j]\n    right[j] = U[span + j] - u\n\n    let saved = 0.0\n\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1]\n      const lv = left[j - r]\n      const temp = N[r] / (rv + lv)\n      N[r] = saved + rv * temp\n      saved = lv * temp\n    }\n\n    N[j] = saved\n  }\n\n  return N\n}\n\n/*\nCalculate B-Spline curve points. See The NURBS Book, page 82, algorithm A3.1.\n\np : degree of B-Spline\nU : knot vector\nP : control points (x, y, z, w)\nu : parametric point\n\nreturns point for given u\n*/\nfunction calcBSplinePoint(p, U, P, u) {\n  const span = findSpan(p, u, U)\n  const N = calcBasisFunctions(span, u, p, U)\n  const C = new Vector4(0, 0, 0, 0)\n\n  for (let j = 0; j <= p; ++j) {\n    const point = P[span - p + j]\n    const Nj = N[j]\n    const wNj = point.w * Nj\n    C.x += point.x * wNj\n    C.y += point.y * wNj\n    C.z += point.z * wNj\n    C.w += point.w * Nj\n  }\n\n  return C\n}\n\n/*\nCalculate basis functions derivatives. See The NURBS Book, page 72, algorithm A2.3.\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nn    : number of derivatives to calculate\nU    : knot vector\n\nreturns array[n+1][p+1] with basis functions derivatives\n*/\nfunction calcBasisFunctionDerivatives(span, u, p, n, U) {\n  const zeroArr = []\n  for (let i = 0; i <= p; ++i) zeroArr[i] = 0.0\n\n  const ders = []\n\n  for (let i = 0; i <= n; ++i) ders[i] = zeroArr.slice(0)\n\n  const ndu = []\n\n  for (let i = 0; i <= p; ++i) ndu[i] = zeroArr.slice(0)\n\n  ndu[0][0] = 1.0\n\n  const left = zeroArr.slice(0)\n  const right = zeroArr.slice(0)\n\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j]\n    right[j] = U[span + j] - u\n\n    let saved = 0.0\n\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1]\n      const lv = left[j - r]\n      ndu[j][r] = rv + lv\n\n      const temp = ndu[r][j - 1] / ndu[j][r]\n      ndu[r][j] = saved + rv * temp\n      saved = lv * temp\n    }\n\n    ndu[j][j] = saved\n  }\n\n  for (let j = 0; j <= p; ++j) {\n    ders[0][j] = ndu[j][p]\n  }\n\n  for (let r = 0; r <= p; ++r) {\n    let s1 = 0\n    let s2 = 1\n\n    const a = []\n    for (let i = 0; i <= p; ++i) {\n      a[i] = zeroArr.slice(0)\n    }\n\n    a[0][0] = 1.0\n\n    for (let k = 1; k <= n; ++k) {\n      let d = 0.0\n      const rk = r - k\n      const pk = p - k\n\n      if (r >= k) {\n        a[s2][0] = a[s1][0] / ndu[pk + 1][rk]\n        d = a[s2][0] * ndu[rk][pk]\n      }\n\n      const j1 = rk >= -1 ? 1 : -rk\n      const j2 = r - 1 <= pk ? k - 1 : p - r\n\n      for (let j = j1; j <= j2; ++j) {\n        a[s2][j] = (a[s1][j] - a[s1][j - 1]) / ndu[pk + 1][rk + j]\n        d += a[s2][j] * ndu[rk + j][pk]\n      }\n\n      if (r <= pk) {\n        a[s2][k] = -a[s1][k - 1] / ndu[pk + 1][r]\n        d += a[s2][k] * ndu[r][pk]\n      }\n\n      ders[k][r] = d\n\n      const j = s1\n      s1 = s2\n      s2 = j\n    }\n  }\n\n  let r = p\n\n  for (let k = 1; k <= n; ++k) {\n    for (let j = 0; j <= p; ++j) {\n      ders[k][j] *= r\n    }\n\n    r *= p - k\n  }\n\n  return ders\n}\n\n/*\n\tCalculate derivatives of a B-Spline. See The NURBS Book, page 93, algorithm A3.2.\n\n\tp  : degree\n\tU  : knot vector\n\tP  : control points\n\tu  : Parametric points\n\tnd : number of derivatives\n\n\treturns array[d+1] with derivatives\n\t*/\nfunction calcBSplineDerivatives(p, U, P, u, nd) {\n  const du = nd < p ? nd : p\n  const CK = []\n  const span = findSpan(p, u, U)\n  const nders = calcBasisFunctionDerivatives(span, u, p, du, U)\n  const Pw = []\n\n  for (let i = 0; i < P.length; ++i) {\n    const point = P[i].clone()\n    const w = point.w\n\n    point.x *= w\n    point.y *= w\n    point.z *= w\n\n    Pw[i] = point\n  }\n\n  for (let k = 0; k <= du; ++k) {\n    const point = Pw[span - p].clone().multiplyScalar(nders[k][0])\n\n    for (let j = 1; j <= p; ++j) {\n      point.add(Pw[span - p + j].clone().multiplyScalar(nders[k][j]))\n    }\n\n    CK[k] = point\n  }\n\n  for (let k = du + 1; k <= nd + 1; ++k) {\n    CK[k] = new Vector4(0, 0, 0)\n  }\n\n  return CK\n}\n\n/*\nCalculate \"K over I\"\n\nreturns k!/(i!(k-i)!)\n*/\nfunction calcKoverI(k, i) {\n  let nom = 1\n\n  for (let j = 2; j <= k; ++j) {\n    nom *= j\n  }\n\n  let denom = 1\n\n  for (let j = 2; j <= i; ++j) {\n    denom *= j\n  }\n\n  for (let j = 2; j <= k - i; ++j) {\n    denom *= j\n  }\n\n  return nom / denom\n}\n\n/*\nCalculate derivatives (0-nd) of rational curve. See The NURBS Book, page 127, algorithm A4.2.\n\nPders : result of function calcBSplineDerivatives\n\nreturns array with derivatives for rational curve.\n*/\nfunction calcRationalCurveDerivatives(Pders) {\n  const nd = Pders.length\n  const Aders = []\n  const wders = []\n\n  for (let i = 0; i < nd; ++i) {\n    const point = Pders[i]\n    Aders[i] = new Vector3(point.x, point.y, point.z)\n    wders[i] = point.w\n  }\n\n  const CK = []\n\n  for (let k = 0; k < nd; ++k) {\n    const v = Aders[k].clone()\n\n    for (let i = 1; i <= k; ++i) {\n      v.sub(CK[k - i].clone().multiplyScalar(calcKoverI(k, i) * wders[i]))\n    }\n\n    CK[k] = v.divideScalar(wders[0])\n  }\n\n  return CK\n}\n\n/*\nCalculate NURBS curve derivatives. See The NURBS Book, page 127, algorithm A4.2.\n\np  : degree\nU  : knot vector\nP  : control points in homogeneous space\nu  : parametric points\nnd : number of derivatives\n\nreturns array with derivatives.\n*/\nfunction calcNURBSDerivatives(p, U, P, u, nd) {\n  const Pders = calcBSplineDerivatives(p, U, P, u, nd)\n  return calcRationalCurveDerivatives(Pders)\n}\n\n/*\nCalculate rational B-Spline surface point. See The NURBS Book, page 134, algorithm A4.3.\n\np1, p2 : degrees of B-Spline surface\nU1, U2 : knot vectors\nP      : control points (x, y, z, w)\nu, v   : parametric values\n\nreturns point for given (u, v)\n*/\nfunction calcSurfacePoint(p, q, U, V, P, u, v, target) {\n  const uspan = findSpan(p, u, U)\n  const vspan = findSpan(q, v, V)\n  const Nu = calcBasisFunctions(uspan, u, p, U)\n  const Nv = calcBasisFunctions(vspan, v, q, V)\n  const temp = []\n\n  for (let l = 0; l <= q; ++l) {\n    temp[l] = new Vector4(0, 0, 0, 0)\n    for (let k = 0; k <= p; ++k) {\n      const point = P[uspan - p + k][vspan - q + l].clone()\n      const w = point.w\n      point.x *= w\n      point.y *= w\n      point.z *= w\n      temp[l].add(point.multiplyScalar(Nu[k]))\n    }\n  }\n\n  const Sw = new Vector4(0, 0, 0, 0)\n  for (let l = 0; l <= q; ++l) {\n    Sw.add(temp[l].multiplyScalar(Nv[l]))\n  }\n\n  Sw.divideScalar(Sw.w)\n  target.set(Sw.x, Sw.y, Sw.z)\n}\n\nexport {\n  findSpan,\n  calcBasisFunctions,\n  calcBSplinePoint,\n  calcBasisFunctionDerivatives,\n  calcBSplineDerivatives,\n  calcKoverI,\n  calcRationalCurveDerivatives,\n  calcNURBSDerivatives,\n  calcSurfacePoint,\n}\n"], "names": ["r", "j"], "mappings": ";AAqBA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,QAAM,IAAI,EAAE,SAAS,IAAI;AAEzB,MAAI,KAAK,EAAE,CAAC,GAAG;AACb,WAAO,IAAI;AAAA,EACZ;AAED,MAAI,KAAK,EAAE,CAAC,GAAG;AACb,WAAO;AAAA,EACR;AAED,MAAI,MAAM;AACV,MAAI,OAAO;AACX,MAAI,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AAErC,SAAO,IAAI,EAAE,GAAG,KAAK,KAAK,EAAE,MAAM,CAAC,GAAG;AACpC,QAAI,IAAI,EAAE,GAAG,GAAG;AACd,aAAO;AAAA,IACb,OAAW;AACL,YAAM;AAAA,IACP;AAED,UAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AAAA,EAClC;AAED,SAAO;AACT;AAYA,SAAS,mBAAmB,MAAM,GAAG,GAAG,GAAG;AACzC,QAAM,IAAI,CAAE;AACZ,QAAM,OAAO,CAAE;AACf,QAAM,QAAQ,CAAE;AAChB,IAAE,CAAC,IAAI;AAEP,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,SAAK,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC;AAC5B,UAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI;AAEzB,QAAI,QAAQ;AAEZ,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAM,KAAK,MAAM,IAAI,CAAC;AACtB,YAAM,KAAK,KAAK,IAAI,CAAC;AACrB,YAAM,OAAO,EAAE,CAAC,KAAK,KAAK;AAC1B,QAAE,CAAC,IAAI,QAAQ,KAAK;AACpB,cAAQ,KAAK;AAAA,IACd;AAED,MAAE,CAAC,IAAI;AAAA,EACR;AAED,SAAO;AACT;AAYA,SAAS,iBAAiB,GAAG,GAAG,GAAG,GAAG;AACpC,QAAM,OAAO,SAAS,GAAG,GAAG,CAAC;AAC7B,QAAM,IAAI,mBAAmB,MAAM,GAAG,GAAG,CAAC;AAC1C,QAAM,IAAI,IAAI,QAAQ,GAAG,GAAG,GAAG,CAAC;AAEhC,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,UAAM,QAAQ,EAAE,OAAO,IAAI,CAAC;AAC5B,UAAM,KAAK,EAAE,CAAC;AACd,UAAM,MAAM,MAAM,IAAI;AACtB,MAAE,KAAK,MAAM,IAAI;AACjB,MAAE,KAAK,MAAM,IAAI;AACjB,MAAE,KAAK,MAAM,IAAI;AACjB,MAAE,KAAK,MAAM,IAAI;AAAA,EAClB;AAED,SAAO;AACT;AAaA,SAAS,6BAA6B,MAAM,GAAG,GAAG,GAAG,GAAG;AACtD,QAAM,UAAU,CAAE;AAClB,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE;AAAG,YAAQ,CAAC,IAAI;AAE1C,QAAM,OAAO,CAAE;AAEf,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE;AAAG,SAAK,CAAC,IAAI,QAAQ,MAAM,CAAC;AAEtD,QAAM,MAAM,CAAE;AAEd,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE;AAAG,QAAI,CAAC,IAAI,QAAQ,MAAM,CAAC;AAErD,MAAI,CAAC,EAAE,CAAC,IAAI;AAEZ,QAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,QAAM,QAAQ,QAAQ,MAAM,CAAC;AAE7B,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,SAAK,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC;AAC5B,UAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI;AAEzB,QAAI,QAAQ;AAEZ,aAASA,KAAI,GAAGA,KAAI,GAAG,EAAEA,IAAG;AAC1B,YAAM,KAAK,MAAMA,KAAI,CAAC;AACtB,YAAM,KAAK,KAAK,IAAIA,EAAC;AACrB,UAAI,CAAC,EAAEA,EAAC,IAAI,KAAK;AAEjB,YAAM,OAAO,IAAIA,EAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,EAAEA,EAAC;AACrC,UAAIA,EAAC,EAAE,CAAC,IAAI,QAAQ,KAAK;AACzB,cAAQ,KAAK;AAAA,IACd;AAED,QAAI,CAAC,EAAE,CAAC,IAAI;AAAA,EACb;AAED,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,SAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;AAAA,EACtB;AAED,WAASA,KAAI,GAAGA,MAAK,GAAG,EAAEA,IAAG;AAC3B,QAAI,KAAK;AACT,QAAI,KAAK;AAET,UAAM,IAAI,CAAE;AACZ,aAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,QAAE,CAAC,IAAI,QAAQ,MAAM,CAAC;AAAA,IACvB;AAED,MAAE,CAAC,EAAE,CAAC,IAAI;AAEV,aAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,UAAI,IAAI;AACR,YAAM,KAAKA,KAAI;AACf,YAAM,KAAK,IAAI;AAEf,UAAIA,MAAK,GAAG;AACV,UAAE,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE;AACpC,YAAI,EAAE,EAAE,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,EAAE;AAAA,MAC1B;AAED,YAAM,KAAK,MAAM,KAAK,IAAI,CAAC;AAC3B,YAAM,KAAKA,KAAI,KAAK,KAAK,IAAI,IAAI,IAAIA;AAErC,eAASC,KAAI,IAAIA,MAAK,IAAI,EAAEA,IAAG;AAC7B,UAAE,EAAE,EAAEA,EAAC,KAAK,EAAE,EAAE,EAAEA,EAAC,IAAI,EAAE,EAAE,EAAEA,KAAI,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE,KAAKA,EAAC;AACzD,aAAK,EAAE,EAAE,EAAEA,EAAC,IAAI,IAAI,KAAKA,EAAC,EAAE,EAAE;AAAA,MAC/B;AAED,UAAID,MAAK,IAAI;AACX,UAAE,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,EAAEA,EAAC;AACxC,aAAK,EAAE,EAAE,EAAE,CAAC,IAAI,IAAIA,EAAC,EAAE,EAAE;AAAA,MAC1B;AAED,WAAK,CAAC,EAAEA,EAAC,IAAI;AAEb,YAAM,IAAI;AACV,WAAK;AACL,WAAK;AAAA,IACN;AAAA,EACF;AAED,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,aAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,WAAK,CAAC,EAAE,CAAC,KAAK;AAAA,IACf;AAED,SAAK,IAAI;AAAA,EACV;AAED,SAAO;AACT;AAaA,SAAS,uBAAuB,GAAG,GAAG,GAAG,GAAG,IAAI;AAC9C,QAAM,KAAK,KAAK,IAAI,KAAK;AACzB,QAAM,KAAK,CAAE;AACb,QAAM,OAAO,SAAS,GAAG,GAAG,CAAC;AAC7B,QAAM,QAAQ,6BAA6B,MAAM,GAAG,GAAG,IAAI,CAAC;AAC5D,QAAM,KAAK,CAAE;AAEb,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,UAAM,QAAQ,EAAE,CAAC,EAAE,MAAO;AAC1B,UAAM,IAAI,MAAM;AAEhB,UAAM,KAAK;AACX,UAAM,KAAK;AACX,UAAM,KAAK;AAEX,OAAG,CAAC,IAAI;AAAA,EACT;AAED,WAAS,IAAI,GAAG,KAAK,IAAI,EAAE,GAAG;AAC5B,UAAM,QAAQ,GAAG,OAAO,CAAC,EAAE,QAAQ,eAAe,MAAM,CAAC,EAAE,CAAC,CAAC;AAE7D,aAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,YAAM,IAAI,GAAG,OAAO,IAAI,CAAC,EAAE,MAAO,EAAC,eAAe,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IAC/D;AAED,OAAG,CAAC,IAAI;AAAA,EACT;AAED,WAAS,IAAI,KAAK,GAAG,KAAK,KAAK,GAAG,EAAE,GAAG;AACrC,OAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,EAC5B;AAED,SAAO;AACT;AAOA,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,MAAM;AAEV,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,WAAO;AAAA,EACR;AAED,MAAI,QAAQ;AAEZ,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,aAAS;AAAA,EACV;AAED,WAAS,IAAI,GAAG,KAAK,IAAI,GAAG,EAAE,GAAG;AAC/B,aAAS;AAAA,EACV;AAED,SAAO,MAAM;AACf;AASA,SAAS,6BAA6B,OAAO;AAC3C,QAAM,KAAK,MAAM;AACjB,QAAM,QAAQ,CAAE;AAChB,QAAM,QAAQ,CAAE;AAEhB,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAM,QAAQ,MAAM,CAAC;AACrB,UAAM,CAAC,IAAI,IAAI,QAAQ,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAChD,UAAM,CAAC,IAAI,MAAM;AAAA,EAClB;AAED,QAAM,KAAK,CAAE;AAEb,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAM,IAAI,MAAM,CAAC,EAAE,MAAO;AAE1B,aAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,QAAE,IAAI,GAAG,IAAI,CAAC,EAAE,MAAO,EAAC,eAAe,WAAW,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC;AAAA,IACpE;AAED,OAAG,CAAC,IAAI,EAAE,aAAa,MAAM,CAAC,CAAC;AAAA,EAChC;AAED,SAAO;AACT;AAaA,SAAS,qBAAqB,GAAG,GAAG,GAAG,GAAG,IAAI;AAC5C,QAAM,QAAQ,uBAAuB,GAAG,GAAG,GAAG,GAAG,EAAE;AACnD,SAAO,6BAA6B,KAAK;AAC3C;AAYA,SAAS,iBAAiB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ;AACrD,QAAM,QAAQ,SAAS,GAAG,GAAG,CAAC;AAC9B,QAAM,QAAQ,SAAS,GAAG,GAAG,CAAC;AAC9B,QAAM,KAAK,mBAAmB,OAAO,GAAG,GAAG,CAAC;AAC5C,QAAM,KAAK,mBAAmB,OAAO,GAAG,GAAG,CAAC;AAC5C,QAAM,OAAO,CAAE;AAEf,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,SAAK,CAAC,IAAI,IAAI,QAAQ,GAAG,GAAG,GAAG,CAAC;AAChC,aAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,YAAM,QAAQ,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE,MAAO;AACrD,YAAM,IAAI,MAAM;AAChB,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,WAAK,CAAC,EAAE,IAAI,MAAM,eAAe,GAAG,CAAC,CAAC,CAAC;AAAA,IACxC;AAAA,EACF;AAED,QAAM,KAAK,IAAI,QAAQ,GAAG,GAAG,GAAG,CAAC;AACjC,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,OAAG,IAAI,KAAK,CAAC,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC;AAAA,EACrC;AAED,KAAG,aAAa,GAAG,CAAC;AACpB,SAAO,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7B;"}