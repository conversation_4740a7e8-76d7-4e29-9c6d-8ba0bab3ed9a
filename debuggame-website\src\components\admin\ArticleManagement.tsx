'use client';

import { useState, useEffect } from 'react';
import { Article, ArticleFilter, ArticleStats, ArticleCategory, ArticleStatus } from '@/types/article';
import { ArticleService } from '@/lib/articleService';

interface ArticleManagementProps {
  onClose: () => void;
}

export default function ArticleManagement({ onClose }: ArticleManagementProps) {
  const [activeTab, setActiveTab] = useState<'list' | 'add' | 'stats'>('list');
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<ArticleCategory[]>([]);
  const [stats, setStats] = useState<ArticleStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [editingArticle, setEditingArticle] = useState<Article | null>(null);

  useEffect(() => {
    loadArticles();
    loadCategories();
    if (activeTab === 'stats') {
      loadStats();
    }
  }, [activeTab]);

  const loadArticles = async () => {
    try {
      setLoading(true);
      const articlesData = await ArticleService.getAllArticles();
      setArticles(articlesData);
    } catch (error) {
      console.error('Failed to load articles:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await ArticleService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await ArticleService.getArticleStats();
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleDeleteArticle = async (id: string) => {
    if (!confirm('确定要删除这篇文章吗？')) return;
    
    try {
      await ArticleService.deleteArticle(id);
      await loadArticles();
    } catch (error) {
      console.error('Failed to delete article:', error);
    }
  };

  const handleEditArticle = (article: Article) => {
    setEditingArticle(article);
    setActiveTab('add');
  };

  const handleSaveArticle = async (articleData: any) => {
    try {
      if (editingArticle) {
        await ArticleService.updateArticle(editingArticle.id, articleData);
      } else {
        await ArticleService.createArticle(articleData);
      }
      setEditingArticle(null);
      setActiveTab('list');
      await loadArticles();
    } catch (error) {
      console.error('Failed to save article:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-slate-800 rounded-lg border border-white/10 w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-2xl font-bold text-white">文章管理</h2>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-white/10">
          <button
            onClick={() => setActiveTab('list')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'list'
                ? 'text-purple-400 border-b-2 border-purple-400'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            文章列表
          </button>
          <button
            onClick={() => setActiveTab('add')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'add'
                ? 'text-purple-400 border-b-2 border-purple-400'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            {editingArticle ? '编辑文章' : '添加文章'}
          </button>
          <button
            onClick={() => setActiveTab('stats')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'stats'
                ? 'text-purple-400 border-b-2 border-purple-400'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            统计信息
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {activeTab === 'list' && (
            <ArticleList
              articles={articles}
              loading={loading}
              onEdit={handleEditArticle}
              onDelete={handleDeleteArticle}
            />
          )}
          
          {activeTab === 'add' && (
            <ArticleForm
              article={editingArticle}
              categories={categories}
              onSave={handleSaveArticle}
              onCancel={() => {
                setEditingArticle(null);
                setActiveTab('list');
              }}
            />
          )}
          
          {activeTab === 'stats' && (
            <ArticleStatsView stats={stats} />
          )}
        </div>
      </div>
    </div>
  );
}

interface ArticleListProps {
  articles: Article[];
  loading: boolean;
  onEdit: (article: Article) => void;
  onDelete: (id: string) => void;
}

function ArticleList({ articles, loading, onEdit, onDelete }: ArticleListProps) {
  if (loading) {
    return <div className="text-center py-8 text-white">加载中...</div>;
  }

  return (
    <div className="space-y-4">
      {articles.map((article) => (
        <div
          key={article.id}
          className="bg-white/5 rounded-lg border border-white/10 p-4"
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span
                  className="inline-block px-2 py-1 rounded text-xs font-medium"
                  style={{ 
                    backgroundColor: `${article.category.color}20`, 
                    color: article.category.color 
                  }}
                >
                  {article.category.name}
                </span>
                <span className={`px-2 py-1 rounded text-xs ${
                  article.status === ArticleStatus.PUBLISHED
                    ? 'bg-green-600/20 text-green-400'
                    : article.status === ArticleStatus.DRAFT
                    ? 'bg-yellow-600/20 text-yellow-400'
                    : 'bg-gray-600/20 text-gray-400'
                }`}>
                  {article.status === ArticleStatus.PUBLISHED ? '已发布' :
                   article.status === ArticleStatus.DRAFT ? '草稿' : '已归档'}
                </span>
              </div>
              
              <h3 className="text-white font-semibold text-lg mb-1">
                {article.title}
              </h3>
              
              <p className="text-slate-300 text-sm mb-2 line-clamp-2">
                {article.excerpt}
              </p>
              
              <div className="flex items-center gap-4 text-xs text-slate-400">
                <span>作者: {article.author.name}</span>
                <span>浏览: {article.views}</span>
                <span>点赞: {article.likes}</span>
                <span>评论: {article.comments.length}</span>
                <span>更新: {new Date(article.updatedAt).toLocaleDateString('zh-CN')}</span>
              </div>
            </div>
            
            <div className="flex gap-2 ml-4">
              <button
                onClick={() => onEdit(article)}
                className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
              >
                编辑
              </button>
              <button
                onClick={() => onDelete(article.id)}
                className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      ))}
      
      {articles.length === 0 && (
        <div className="text-center py-8 text-slate-400">
          暂无文章
        </div>
      )}
    </div>
  );
}

interface ArticleFormProps {
  article: Article | null;
  categories: ArticleCategory[];
  onSave: (data: any) => void;
  onCancel: () => void;
}

function ArticleForm({ article, categories, onSave, onCancel }: ArticleFormProps) {
  const [formData, setFormData] = useState({
    title: article?.title || '',
    content: article?.content || '',
    excerpt: article?.excerpt || '',
    categoryId: article?.category.id || '',
    tags: article?.tags.join(', ') || '',
    status: article?.status || ArticleStatus.DRAFT,
    slug: article?.slug || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const category = categories.find(c => c.id === formData.categoryId);
    if (!category) return;

    const articleData = {
      title: formData.title,
      content: formData.content,
      excerpt: formData.excerpt,
      category,
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
      status: formData.status,
      slug: formData.slug || formData.title.toLowerCase().replace(/\s+/g, '-'),
      author: article?.author || {
        id: 'admin',
        name: '管理员',
        role: 'admin'
      },
      relatedGames: article?.relatedGames || []
    };

    onSave(articleData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-white font-medium mb-2">标题</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            required
          />
        </div>
        
        <div>
          <label className="block text-white font-medium mb-2">URL别名</label>
          <input
            type="text"
            value={formData.slug}
            onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            placeholder="自动生成"
          />
        </div>
      </div>

      <div>
        <label className="block text-white font-medium mb-2">摘要</label>
        <textarea
          value={formData.excerpt}
          onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
          className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
          rows={3}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-white font-medium mb-2">分类</label>
          <select
            value={formData.categoryId}
            onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            required
          >
            <option value="">选择分类</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-white font-medium mb-2">标签</label>
          <input
            type="text"
            value={formData.tags}
            onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            placeholder="用逗号分隔"
          />
        </div>
        
        <div>
          <label className="block text-white font-medium mb-2">状态</label>
          <select
            value={formData.status}
            onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as ArticleStatus }))}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value={ArticleStatus.DRAFT}>草稿</option>
            <option value={ArticleStatus.PUBLISHED}>发布</option>
            <option value={ArticleStatus.ARCHIVED}>归档</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-white font-medium mb-2">内容</label>
        <textarea
          value={formData.content}
          onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
          className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
          rows={12}
          required
        />
      </div>

      <div className="flex gap-4">
        <button
          type="submit"
          className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
        >
          {article ? '更新文章' : '创建文章'}
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="px-6 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors"
        >
          取消
        </button>
      </div>
    </form>
  );
}

interface ArticleStatsViewProps {
  stats: ArticleStats | null;
}

function ArticleStatsView({ stats }: ArticleStatsViewProps) {
  if (!stats) {
    return <div className="text-center py-8 text-white">加载中...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white/5 rounded-lg border border-white/10 p-4">
          <div className="text-2xl font-bold text-white">{stats.totalArticles}</div>
          <div className="text-slate-400 text-sm">总文章数</div>
        </div>
        <div className="bg-white/5 rounded-lg border border-white/10 p-4">
          <div className="text-2xl font-bold text-green-400">{stats.publishedArticles}</div>
          <div className="text-slate-400 text-sm">已发布</div>
        </div>
        <div className="bg-white/5 rounded-lg border border-white/10 p-4">
          <div className="text-2xl font-bold text-blue-400">{stats.totalViews}</div>
          <div className="text-slate-400 text-sm">总浏览量</div>
        </div>
        <div className="bg-white/5 rounded-lg border border-white/10 p-4">
          <div className="text-2xl font-bold text-red-400">{stats.totalLikes}</div>
          <div className="text-slate-400 text-sm">总点赞数</div>
        </div>
      </div>

      {/* Category Distribution */}
      <div className="bg-white/5 rounded-lg border border-white/10 p-6">
        <h3 className="text-white font-bold text-lg mb-4">分类分布</h3>
        <div className="space-y-3">
          {Object.entries(stats.categoryDistribution).map(([category, count]) => (
            <div key={category} className="flex items-center justify-between">
              <span className="text-slate-300">{category}</span>
              <div className="flex items-center gap-2">
                <div className="w-32 bg-white/10 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full"
                    style={{ width: `${(count / stats.totalArticles) * 100}%` }}
                  />
                </div>
                <span className="text-white font-medium w-8 text-right">{count}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Popular Tags */}
      <div className="bg-white/5 rounded-lg border border-white/10 p-6">
        <h3 className="text-white font-bold text-lg mb-4">热门标签</h3>
        <div className="flex flex-wrap gap-2">
          {stats.popularTags.slice(0, 10).map((tagData) => (
            <span
              key={tagData.tag}
              className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm"
            >
              #{tagData.tag} ({tagData.count})
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}
