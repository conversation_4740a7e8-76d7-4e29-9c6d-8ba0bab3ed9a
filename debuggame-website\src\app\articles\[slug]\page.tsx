'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Article } from '@/types/article';
import { ArticleService } from '@/lib/articleService';

export default function ArticleDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [liked, setLiked] = useState(false);

  useEffect(() => {
    if (slug) {
      loadArticle();
    }
  }, [slug]);

  const loadArticle = async () => {
    try {
      setLoading(true);
      const articleData = await ArticleService.getArticleBySlug(slug);
      if (articleData) {
        setArticle(articleData);
        // 增加浏览量
        await ArticleService.incrementViews(articleData.id);
      }
    } catch (error) {
      console.error('Failed to load article:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async () => {
    if (!article || liked) return;
    
    try {
      await ArticleService.likeArticle(article.id);
      setArticle(prev => prev ? { ...prev, likes: prev.likes + 1 } : null);
      setLiked(true);
    } catch (error) {
      console.error('Failed to like article:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">加载中...</div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-white text-xl mb-4">文章未找到</div>
          <Link
            href="/articles"
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            返回文章列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        {/* Navigation */}
        <div className="mb-6">
          <Link
            href="/articles"
            className="inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors"
          >
            ← 返回文章列表
          </Link>
        </div>

        {/* Article Header */}
        <header className="mb-8">
          {/* Category */}
          <div className="mb-4">
            <span
              className="inline-block px-3 py-1 rounded-full text-sm font-medium"
              style={{ 
                backgroundColor: `${article.category.color}20`, 
                color: article.category.color 
              }}
            >
              {article.category.icon} {article.category.name}
            </span>
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {article.title}
          </h1>

          {/* Meta Info */}
          <div className="flex flex-wrap items-center gap-4 text-slate-300 text-sm mb-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-xs">
                {article.author.name[0]}
              </div>
              <span>{article.author.name}</span>
            </div>
            <span>•</span>
            <span>{new Date(article.publishedAt!).toLocaleDateString('zh-CN')}</span>
            <span>•</span>
            <span>👁️ {article.views} 次浏览</span>
            <span>•</span>
            <span>❤️ {article.likes} 个赞</span>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            {article.tags.map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-white/10 text-slate-300 rounded text-sm"
              >
                #{tag}
              </span>
            ))}
          </div>
        </header>

        {/* Article Content */}
        <article className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-8 mb-8">
          <div className="prose prose-invert prose-purple max-w-none">
            <div 
              className="text-slate-200 leading-relaxed"
              dangerouslySetInnerHTML={{ 
                __html: article.content.replace(/\n/g, '<br>').replace(/#{1,6}\s/g, '<h3 class="text-white font-bold text-xl mt-6 mb-4">').replace(/<h3[^>]*>/g, '<h3 class="text-white font-bold text-xl mt-6 mb-4">') 
              }}
            />
          </div>
        </article>

        {/* Action Buttons */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={handleLike}
            disabled={liked}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
              liked
                ? 'bg-red-600 text-white cursor-not-allowed'
                : 'bg-white/10 text-slate-300 hover:bg-red-600 hover:text-white'
            }`}
          >
            ❤️ {liked ? '已点赞' : '点赞'} ({article.likes})
          </button>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-white/10 text-slate-300 hover:bg-blue-600 hover:text-white rounded-lg transition-colors">
            📤 分享
          </button>
        </div>

        {/* Author Info */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6 mb-8">
          <h3 className="text-white font-bold text-lg mb-4">关于作者</h3>
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">
              {article.author.name[0]}
            </div>
            <div>
              <h4 className="text-white font-semibold">{article.author.name}</h4>
              <p className="text-slate-300 text-sm mt-1">
                {article.author.bio || '这位作者很神秘，还没有留下简介。'}
              </p>
              <div className="mt-2">
                <span className="inline-block px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs">
                  {article.author.role === 'admin' ? '管理员' : 
                   article.author.role === 'editor' ? '编辑' : '贡献者'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
          <h3 className="text-white font-bold text-lg mb-4">
            评论 ({article.comments.length})
          </h3>
          
          {article.comments.length === 0 ? (
            <p className="text-slate-400">暂无评论，来发表第一条评论吧！</p>
          ) : (
            <div className="space-y-4">
              {article.comments.map((comment) => (
                <div key={comment.id} className="border-b border-white/10 pb-4 last:border-b-0">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs">
                      {comment.author.name[0]}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-white font-medium text-sm">{comment.author.name}</span>
                        <span className="text-slate-400 text-xs">
                          {new Date(comment.createdAt).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                      <p className="text-slate-300 text-sm">{comment.content}</p>
                      <div className="mt-2">
                        <button className="text-slate-400 hover:text-white text-xs">
                          ❤️ {comment.likes}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Comment Form */}
          <div className="mt-6 pt-6 border-t border-white/10">
            <textarea
              placeholder="写下你的评论..."
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              rows={3}
            />
            <div className="mt-3 flex justify-end">
              <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                发表评论
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
