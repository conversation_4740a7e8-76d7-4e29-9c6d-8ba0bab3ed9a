'use client';

import { GameStats as GameStatsType } from '@/types/game';

interface GameStatsProps {
  stats: GameStatsType;
}

export default function GameStats({ stats }: GameStatsProps) {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">游戏统计数据</h2>
      </div>

      {/* 总览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="游戏总数"
          value={stats.totalGames}
          icon="🎮"
          color="bg-blue-600"
        />
        <StatCard
          title="平均评分"
          value={stats.averageRating.toFixed(1)}
          suffix="/10"
          icon="⭐"
          color="bg-yellow-600"
        />
        <StatCard
          title="游戏类型"
          value={Object.keys(stats.genreDistribution).length}
          icon="🏷️"
          color="bg-green-600"
        />
        <StatCard
          title="支持平台"
          value={Object.keys(stats.platformDistribution).length}
          icon="💻"
          color="bg-purple-600"
        />
      </div>

      {/* 分布图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 类型分布 */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">游戏类型分布</h3>
          <div className="space-y-3">
            {Object.entries(stats.genreDistribution)
              .sort(([,a], [,b]) => b - a)
              .map(([genre, count]) => (
                <div key={genre} className="flex items-center justify-between">
                  <span className="text-slate-300">{genre}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-white/10 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full"
                        style={{
                          width: `${(count / Math.max(...Object.values(stats.genreDistribution))) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-white font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* 平台分布 */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">平台分布</h3>
          <div className="space-y-3">
            {Object.entries(stats.platformDistribution)
              .sort(([,a], [,b]) => b - a)
              .map(([platform, count]) => (
                <div key={platform} className="flex items-center justify-between">
                  <span className="text-slate-300">{platform}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-white/10 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{
                          width: `${(count / Math.max(...Object.values(stats.platformDistribution))) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-white font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* 年份分布 */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">发布年份分布</h3>
          <div className="space-y-3">
            {Object.entries(stats.yearDistribution)
              .sort(([a], [b]) => parseInt(b) - parseInt(a))
              .map(([year, count]) => (
                <div key={year} className="flex items-center justify-between">
                  <span className="text-slate-300">{year}年</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-white/10 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{
                          width: `${(count / Math.max(...Object.values(stats.yearDistribution))) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-white font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* 数据洞察 */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">数据洞察</h3>
          <div className="space-y-4 text-sm">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
              <div>
                <div className="text-white font-medium">最受欢迎的类型</div>
                <div className="text-slate-300">
                  {Object.entries(stats.genreDistribution)
                    .sort(([,a], [,b]) => b - a)[0]?.[0] || '暂无数据'}
                </div>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <div>
                <div className="text-white font-medium">主要平台</div>
                <div className="text-slate-300">
                  {Object.entries(stats.platformDistribution)
                    .sort(([,a], [,b]) => b - a)[0]?.[0] || '暂无数据'}
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
              <div>
                <div className="text-white font-medium">发布高峰年</div>
                <div className="text-slate-300">
                  {Object.entries(stats.yearDistribution)
                    .sort(([,a], [,b]) => b - a)[0]?.[0] || '暂无数据'}年
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
              <div>
                <div className="text-white font-medium">评分状况</div>
                <div className="text-slate-300">
                  {stats.averageRating >= 8 ? '优秀' : 
                   stats.averageRating >= 7 ? '良好' : 
                   stats.averageRating >= 6 ? '一般' : '待提升'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: number | string;
  suffix?: string;
  icon: string;
  color: string;
}

function StatCard({ title, value, suffix = '', icon, color }: StatCardProps) {
  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-slate-400 text-sm">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">
            {value}{suffix}
          </p>
        </div>
        <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center text-2xl`}>
          {icon}
        </div>
      </div>
    </div>
  );
}
