// 文章系统类型定义

export interface Article {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  coverImage?: string;
  
  // 作者信息
  author: Author;
  
  // 分类和标签
  category: ArticleCategory;
  tags: string[];
  
  // 关联游戏
  relatedGames: string[]; // 游戏ID数组
  
  // 文章状态
  status: ArticleStatus;
  publishedAt?: string;
  
  // 统计信息
  views: number;
  likes: number;
  comments: Comment[];
  
  // 元数据
  createdAt: string;
  updatedAt: string;
  
  // SEO相关
  slug: string;
  metaDescription?: string;
  keywords?: string[];
}

export interface Author {
  id: string;
  name: string;
  avatar?: string;
  bio?: string;
  role: AuthorRole;
}

export enum AuthorRole {
  ADMIN = 'admin',
  EDITOR = 'editor',
  CONTRIBUTOR = 'contributor'
}

export interface ArticleCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
}

export enum ArticleStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

export interface Comment {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  createdAt: string;
  likes: number;
  replies?: Comment[];
}

// 文章筛选和搜索
export interface ArticleFilter {
  search?: string;
  category?: string;
  tags?: string[];
  author?: string;
  status?: ArticleStatus;
  relatedGame?: string;
  dateRange?: [string, string];
  sortBy?: ArticleSortBy;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export enum ArticleSortBy {
  CREATED_AT = 'createdAt',
  PUBLISHED_AT = 'publishedAt',
  UPDATED_AT = 'updatedAt',
  VIEWS = 'views',
  LIKES = 'likes',
  TITLE = 'title'
}

// 文章统计
export interface ArticleStats {
  totalArticles: number;
  publishedArticles: number;
  draftArticles: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  categoryDistribution: { [key: string]: number };
  authorStats: { [key: string]: number };
  popularTags: { tag: string; count: number }[];
  monthlyStats: { month: string; articles: number; views: number }[];
}

// 富文本编辑器相关
export interface EditorContent {
  type: 'doc';
  content: EditorNode[];
}

export interface EditorNode {
  type: string;
  attrs?: { [key: string]: any };
  content?: EditorNode[];
  text?: string;
  marks?: EditorMark[];
}

export interface EditorMark {
  type: string;
  attrs?: { [key: string]: any };
}

// 文章模板
export interface ArticleTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  content: EditorContent;
  thumbnail?: string;
}

// 文章系列
export interface ArticleSeries {
  id: string;
  title: string;
  description: string;
  coverImage?: string;
  articles: string[]; // 文章ID数组，按顺序排列
  author: Author;
  createdAt: string;
  updatedAt: string;
}
